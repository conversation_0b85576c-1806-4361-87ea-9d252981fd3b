DescEnhancer:
system prompt:
You are an expert AI assistant specializing in enhancing incomplete visual descriptions for text-to-image generation. Your primary purpose is to take subjects extracted from narrative text that have ambiguous or missing visual details and provide consistent, appropriate visual descriptions for them.

Your expertise includes:
1. Identifying subjects with incomplete or missing visual descriptions
2. Creating appropriate, consistent visual details for these subjects
3. Maintaining the tone and style of the original narrative
4. Ensuring descriptions are detailed enough for high-quality image generation
5. Providing consistent descriptions for similar character types across different stories

When enhancing descriptions:
- Clearly distinguish between details explicitly stated in the original text and your enhancements
- Maintain consistency with any existing visual details provided
- Consider the time period, setting, and genre of the story
- Provide age-appropriate descriptions for the apparent target audience
- Ensure descriptions are visually coherent and would work well for image generation
- For traditional fairy tales or well-known stories, use culturally familiar visual representations unless otherwise specified

Always mark your enhancements with [ENHANCED] at the beginning of any added details, so it's clear which parts were not in the original text.
user prompt:
I need to enhance incomplete visual descriptions for subjects extracted from a narrative text. Please review the following subjects and provide complete visual descriptions for any that have ambiguous or missing details.  Extracted subjects: <subjextractor.response.subjects>  Ambiguities identified: <subjextractor.response.analysis.ambiguities>  Original narrative text (for context): <router1.response.content>  For each subject with incomplete visual details: 1. Maintain all existing visual details from the original text 2. Add appropriate visual details that are missing 3. Ensure consistency with the narrative context and time period 4. Mark all added details with [ENHANCED] at the beginning
response format:
{
  "name": "description_enhancer",
  "schema": {
    "type": "object",
    "properties": {
      "enhanced_subjects": {
        "type": "array",
        "description": "List of subjects with enhanced descriptions",
        "items": {
          "type": "object",
          "properties": {
            "identifier": {
              "type": "string",
              "description": "Short name/identifier for the subject (same as original)"
            },
            "original_phrase": {
              "type": "string",
              "description": "The original description phrase from the extractor"
            },
            "enhanced_phrase": {
              "type": "string",
              "description": "The enhanced description with complete visual details"
            },
            "category": {
              "type": "string",
              "description": "Category of the subject (character, object, environment, etc.)"
            },
            "enhancement_notes": {
              "type": "string",
              "description": "Notes explaining the enhancements made and reasoning"
            },
            "relationships": {
              "type": "array",
              "description": "Relationships with other subjects (preserved from original)",
              "items": {
                "type": "object",
                "properties": {
                  "related_subject": {
                    "type": "string",
                    "description": "Identifier of the related subject"
                  },
                  "relationship_type": {
                    "type": "string",
                    "description": "Nature of the relationship"
                  },
                  "description": {
                    "type": "string",
                    "description": "Description of how they interact or relate"
                  }
                },
                "required": ["related_subject", "relationship_type"]
              }
            }
          },
          "required": ["identifier", "original_phrase", "enhanced_phrase", "category"]
        }
      },
      "enhancement_summary": {
        "type": "object",
        "description": "Summary of the enhancement process",
        "properties": {
          "subjects_enhanced": {
            "type": "integer",
            "description": "Number of subjects that required enhancement"
          },
          "subjects_unchanged": {
            "type": "integer",
            "description": "Number of subjects that already had complete descriptions"
          },
          "enhancement_approach": {
            "type": "string",
            "description": "General approach used for enhancements (e.g., period-appropriate, genre conventions)"
          }
        }
      }
    },
    "required": ["enhanced_subjects", "enhancement_summary"]
  },
  "strict": true
}