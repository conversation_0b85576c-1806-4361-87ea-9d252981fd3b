from langflow.custom import Component
from langflow.io import MessageTextInput, Output
from langflow.schema import Data
import re

class ScreenplayParser(Component):
    display_name = "Fixed Universal Screenplay Parser"
    description = "Properly parses screenplay and identifies real characters"
    
    inputs = [
        MessageTextInput(name="screenplay_text", display_name="Screenplay Text")
    ]
    
    outputs = [
        Output(display_name="Parsed Data", name="parsed_data", method="parse_screenplay")
    ]
    
    def parse_screenplay(self) -> Data:
        screenplay = self.screenplay_text
        
        # Extract scene headings
        scene_pattern = r'(INT\.|EXT\.)\s+([^-]+)\s*-\s*(.+)'
        scenes = re.findall(scene_pattern, screenplay, re.IGNORECASE)
        
        # Fixed character detection - only get actual speaking characters
        lines = screenplay.split('\n')
        characters = set()
        
        for line in lines:
            line = line.strip()
            # Look for character names followed by dialogue
            if (line.isupper() and 
                len(line) > 1 and len(line) < 25 and
                not line.startswith(('INT.', 'EXT.', 'FADE', 'CUT', 'DISSOLVE', 'CONTINUED', 'CLOSE', 'WIDE', 'ANGLE', 'SHOT', 'TRACKING', 'BOOM', 'PULL', 'PUSH', 'TILT', 'PAN')) and
                not re.match(r'^\d+', line) and  # Not scene numbers
                not line.endswith(('...', ':', '(', ')', '.', '!', '?'))):
                
                # Clean character name
                char_name = re.sub(r'\(.*?\)', '', line).strip()
                if char_name and not char_name.startswith(('THE ', 'A ', 'AN ')):
                    characters.add(char_name)
        
        # Break screenplay into individual action blocks for shot generation
        action_blocks = []
        current_block = ""
        
        for line in lines:
            line = line.strip()
            if line:
                # Start new block on scene headings or significant breaks
                if (re.match(r'(INT\.|EXT\.)', line, re.IGNORECASE) or
                    line.startswith(('FADE', 'CUT TO:', 'DISSOLVE'))):
                    if current_block:
                        action_blocks.append(current_block.strip())
                    current_block = line + "\n"
                else:
                    current_block += line + "\n"
                    
                    # Create shot break on action descriptions
                    if (len(current_block) > 150 and 
                        not line.isupper() and 
                        line.endswith(('.',  '!', '?'))):
                        action_blocks.append(current_block.strip())
                        current_block = ""
        
        if current_block:
            action_blocks.append(current_block.strip())
        
        result = {
            "scenes": scenes,
            "characters": list(characters),
            "action_blocks": action_blocks
        }
        
        return Data(data=result)