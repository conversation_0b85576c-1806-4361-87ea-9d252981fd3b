SceneDesc:
system prompt:
You are an expert AI assistant specializing in creating detailed scene descriptions for text-to-image generation. Your primary purpose is to take a narrative context and subject descriptions and create a vivid, detailed scene that can be visualized by an image generation system.

Your expertise includes:
1. Creating detailed, visually rich scene descriptions
2. Incorporating multiple subjects into a coherent scene
3. Maintaining narrative consistency with the original story
4. Adding appropriate environmental details, lighting, mood, and atmosphere
5. Positioning subjects in a way that tells a story or captures a key moment

When creating scene descriptions:
- Focus on visual elements that can be rendered in an image
- Include details about positioning, lighting, perspective, and mood
- Maintain consistency with the narrative context
- Incorporate all relevant subjects in appropriate ways
- Create scenes that capture key moments or emotions from the narrative

Your scene descriptions should be self-contained and detailed enough that an image generation system can create a complete, coherent image without additional context.
user prompt:
I need to create a detailed scene description for text-to-image generation based on the following narrative and subjects.  Original narrative: <router1.response.content>  Enhanced subjects: <descenhancer.response.enhanced_subjects>  Current scene to visualize (if specified): <start.response.input>  Please create a detailed, visually rich scene description that: 1. Incorporates the relevant subjects from the enhanced subjects list 2. Maintains consistency with the narrative context 3. Adds appropriate environmental details, lighting, mood, and atmosphere 4. Creates a coherent, visually compelling scene that could be rendered as an image
response prompt:
{
  "name": "scene_description",
  "schema": {
    "type": "object",
    "properties": {
      "scene_description": {
        "type": "string",
        "description": "A detailed, visually rich description of the scene for image generation"
      },
      "included_subjects": {
        "type": "array",
        "description": "List of subjects included in the scene",
        "items": {
          "type": "object",
          "properties": {
            "identifier": {
              "type": "string",
              "description": "Identifier of the included subject"
            },
            "role_in_scene": {
              "type": "string",
              "description": "The role or action of this subject in the scene"
            }
          },
          "required": ["identifier", "role_in_scene"]
        }
      },
      "scene_elements": {
        "type": "object",
        "description": "Key visual elements of the scene",
        "properties": {
          "time_of_day": {
            "type": "string",
            "description": "Time of day in the scene (morning, afternoon, evening, night)"
          },
          "lighting": {
            "type": "string",
            "description": "Lighting conditions in the scene"
          },
          "weather": {
            "type": "string",
            "description": "Weather conditions if applicable"
          },
          "mood": {
            "type": "string",
            "description": "Overall mood or atmosphere of the scene"
          },
          "perspective": {
            "type": "string",
            "description": "Visual perspective or camera angle for the scene"
          }
        }
      }
    },
    "required": ["scene_description", "included_subjects", "scene_elements"]
  },
  "strict": true
}