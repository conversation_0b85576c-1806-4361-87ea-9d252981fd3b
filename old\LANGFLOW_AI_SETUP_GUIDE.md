# Langflow AI Integration Setup Guide

## What Was Changed

### 1. Subject Extractor (`001-subject-extractor.py`)
- **REMOVED**: All hardcoded character detection (Little Red <PERSON>, <PERSON>, etc.)
- **REMOVED**: Direct API integration (improper approach)
- **ADDED**: Proper Langflow LLM component integration
- **NEW INPUT**: Language Model connection (connects to any LLM component)

### 2. Description Enhancer (`002-description-enhancer.py`)
- **REMOVED**: Placeholder/stub implementation
- **REMOVED**: Direct API integration (improper approach)
- **ADDED**: Proper Langflow LLM component integration
- **NEW INPUT**: Language Model connection (connects to any LLM component)

## How to Set Up in Langflow (Proper Method)

### Step 1: Add Language Model Component
1. In Langflow, add a **Language Model** component to your workflow
2. Configure the Language Model component:
   - **Provider**: Choose OpenAI, Anthropic, or Google
   - **Model Name**: Select your preferred model (e.g., `gpt-4o`, `claude-3-5-sonnet-20241022`)
   - **API Key**: Enter your API key for the chosen provider
   - **Input**: Connect a Text Input component with your narrative text
   - **System Message**: Use this system message:
   ```
   You are an expert AI assistant specializing in extracting and enhancing visual descriptions from narrative text for text-to-image generation. You excel at identifying subjects (characters, objects, environments) and creating detailed, consistent descriptions. Always respond with valid JSON when requested.
   ```
   - **Temperature**: Set to 0.1 for consistent results

### Step 2: Connect Components
1. Import the updated Subject Extractor and Description Enhancer
2. Connect the **Language Model** output to the **LLM** input of both components
3. This allows you to use ANY LLM provider (OpenAI, Anthropic, Google, etc.)

### Step 3: Supported Providers & Models
Through Langflow's Language Model component, you can use:

**OpenAI:**
- `gpt-4o` (Recommended for complex tasks)
- `gpt-4o-mini` (Fast and cost-effective)
- `o1-preview` (Advanced reasoning)

**Anthropic:**
- `claude-3-5-sonnet-20241022` (Excellent for creative tasks)
- `claude-3-haiku-20240307` (Fast and efficient)

**Google:**
- `gemini-1.5-pro` (Good balance)
- `gemini-1.5-flash` (Fast responses)

## How It Works Now

### Subject Extractor
1. Takes any narrative text as input
2. Uses connected Language Model component with structured prompts
3. Returns JSON with:
   - Extracted subjects with detailed descriptions
   - Categories (character, object, environment, etc.)
   - Relationships between subjects
   - Text references and ambiguities

### Description Enhancer
1. Takes the extracted subjects from step 1
2. Uses connected Language Model to identify incomplete visual descriptions
3. Enhances them with `[ENHANCED]` tags using AI
4. Returns complete visual descriptions ready for image generation

## Benefits of Proper Langflow Integration

✅ **Universal**: Works with ANY story, not just Little Red Riding Hood
✅ **Flexible**: Use ANY LLM provider (OpenAI, Anthropic, Google, etc.)
✅ **Modular**: Easy to swap LLM models without changing component code
✅ **Maintainable**: Follows Langflow best practices and patterns
✅ **Scalable**: No hardcoded API integrations
✅ **Cost-Effective**: Choose the most cost-effective model for your needs

## Complete Workflow Setup (Step-by-Step)

### **Step-by-Step Connection Guide:**

1. **Add Text Input Component**
   - Add a "Text Input" component
   - Enter your narrative text here (story, screenplay, etc.)

2. **Add Language Model Component**
   - Add "Language Model" component
   - **Input**: Connect the Text Input component output to this input
   - **Provider**: Choose OpenAI, Anthropic, or Google
   - **Model Name**: Select model (e.g., `gpt-4o`, `claude-3-5-sonnet-20241022`)
   - **API Key**: Enter your API key
   - **System Message**: Leave EMPTY (our components handle this)
   - **Temperature**: Set to 0.1

3. **Add Subject Extractor Component**
   - Add our "AI Subject Extractor" component
   - **Narrative Text**: Connect the Text Input output here
   - **Language Model**: Connect the Language Model output here

4. **Add Description Enhancer Component**
   - Add our "AI Description Enhancer" component
   - **Extracted Subjects**: Connect Subject Extractor output here
   - **Original Narrative**: Connect the Text Input output here
   - **Language Model**: Connect the Language Model output here

### **Visual Workflow:**

```
[Text Input: "Your story text"]
    ↓ (connects to both)
    ├─→ [Language Model] ← Configure with API key
    │       ↓ (LLM output connects to both)
    │       ├─→ [Subject Extractor] ← Also gets narrative text
    │       │       ↓ (extracted subjects)
    │       └─→ [Description Enhancer] ← Gets subjects + narrative + LLM
    │                   ↓
    └─→ [Enhanced Subjects Output]
```

### **Important Connection Notes:**
- **Text Input** connects to 3 places: Language Model input, Subject Extractor narrative input, Description Enhancer narrative input
- **Language Model** output connects to 2 places: Subject Extractor LLM input, Description Enhancer LLM input
- **Subject Extractor** output connects to: Description Enhancer extracted subjects input

## Testing Your Setup

1. **Input**: Any story text (try a paragraph from any book/script)
2. **Expected Output**: Structured JSON with subjects and descriptions
3. **Check**: Look for `[ENHANCED]` tags in the description enhancer output

## Troubleshooting

### Common Issues:
1. **LLM Connection Error**: Ensure Language Model component is properly connected
2. **API Key Error**: Check your API key in the Language Model component
3. **JSON Parse Error**: The AI response wasn't valid JSON - try a different model
4. **Rate Limits**: Provider rate limits - wait a moment and retry
5. **Empty Results**: Check if your narrative text has clear subjects to extract

### Error Handling:
Both components include fallback error responses, so they won't crash your workflow if the LLM fails.

## Why This Approach is Better

### ❌ **Previous Approach (Wrong)**
- Hardcoded API calls in components
- Required API keys in every component
- Difficult to change models
- Not following Langflow patterns

### ✅ **New Approach (Correct)**
- Uses Langflow's built-in Language Model component
- Single API key configuration
- Easy model switching
- Follows Langflow best practices
- Modular and maintainable

## Quick Setup Checklist

### **Language Model Component Settings:**
- [ ] **Provider**: OpenAI/Anthropic/Google selected
- [ ] **Model Name**: Specific model chosen (e.g., `gpt-4o`)
- [ ] **API Key**: Your API key entered
- [ ] **Input**: Connected to Text Input component
- [ ] **System Message**: Copy the system message provided above
- [ ] **Temperature**: Set to 0.1

### **Subject Extractor Connections:**
- [ ] **Narrative Text**: Connected to Text Input
- [ ] **Language Model**: Connected to Language Model output

### **Description Enhancer Connections:**
- [ ] **Extracted Subjects**: Connected to Subject Extractor output
- [ ] **Original Narrative**: Connected to Text Input
- [ ] **Language Model**: Connected to Language Model output

### **Test Your Setup:**
1. Enter any story text in Text Input
2. Run the workflow
3. Check Subject Extractor output for JSON with subjects
4. Check Description Enhancer output for enhanced descriptions with `[ENHANCED]` tags

You now have a professional, maintainable, and flexible AI-powered subject extraction system!