# 002 Subject Extractor Component

## Overview
**Type:** Hybrid Component (Python + 1 LLM call)
**Purpose:** Extract subjects with robust JSON validation to fix incomplete responses
**Priority:** CRITICAL - This fixes your "possession"... truncation issue

## Why This Component Fixes Your Issues
- **JSON validation with auto-repair** - Fixes truncated responses like "possession"...
- **Retry logic** - If response incomplete, retry with simplified prompt
- **Token management** - Smart prompt sizing to avoid limits
- **Error isolation** - Failures don't break the entire chain

## Input Specification
```python
inputs = [
    DataInput(
        name="parsed_screenplay", 
        display_name="Parsed Screenplay",
        info="Structured screenplay data from parser"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 or other LLM"
    )
]
```

## Output Specification
```json
{
  "subjects": [
    {
      "identifier": "LittleRedRidingHood",
      "exact_phrase": "young girl with red hooded cloak, carrying wicker basket, innocent expression",
      "category": "character",
      "relationships": [
        {
          "related_subject": "Grandmother",
          "relationship_type": "family",
          "description": "granddaughter visiting grandmother"
        },
        {
          "related_subject": "Basket",
          "relationship_type": "possession",
          "description": "carries basket of goodies"
        }
      ],
      "text_references": [
        "Little Red Riding Hood enters the cottage",
        "She carries a basket of goodies"
      ],
      "confidence": 0.95,
      "extraction_notes": "Clear character description from text"
    }
  ],
  "analysis": {
    "subject_count": 5,
    "ambiguities": [
      {
        "subject": "Grandmother",
        "issue": "Physical appearance not described in detail"
      }
    ],
    "processing_notes": "Extraction completed successfully",
    "token_usage": {
      "prompt_tokens": 450,
      "response_tokens": 320,
      "total_tokens": 770
    }
  }
}
```

## Core Functionality

### 1. JSON Validation and Repair
```python
def validate_and_repair_json(self, response_text: str) -> Dict:
    """Fix incomplete JSON responses from LLM."""
    
    # Step 1: Try direct parsing
    try:
        return json.loads(response_text)
    except json.JSONDecodeError:
        pass
    
    # Step 2: Extract JSON from text
    json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
    if json_match:
        try:
            return json.loads(json_match.group())
        except json.JSONDecodeError:
            pass
    
    # Step 3: Repair incomplete JSON
    repaired = self._repair_incomplete_json(response_text)
    if repaired:
        try:
            return json.loads(repaired)
        except json.JSONDecodeError:
            pass
    
    # Step 4: Request completion from LLM
    return self._request_json_completion(response_text)

def _repair_incomplete_json(self, text: str) -> str:
    """Add missing closing brackets to incomplete JSON."""
    if not text.strip():
        return None
    
    # Count brackets
    open_braces = text.count('{')
    close_braces = text.count('}')
    open_brackets = text.count('[')
    close_brackets = text.count(']')
    
    # Add missing closers
    repaired = text
    repaired += '}' * (open_braces - close_braces)
    repaired += ']' * (open_brackets - close_brackets)
    
    return repaired
```

### 2. Smart Prompt Generation
```python
def create_extraction_prompt(self, screenplay_data: Dict) -> str:
    """Create token-efficient prompt for subject extraction."""
    
    # Extract key narrative elements
    scenes = screenplay_data.get("scenes", [])
    characters = screenplay_data.get("characters", [])
    
    # Build concise narrative summary
    narrative_summary = self._build_narrative_summary(scenes)
    character_list = [char["name"] for char in characters]
    
    # Token-aware prompt construction
    base_prompt = f"""Extract visual subjects from this narrative for image generation.

Narrative: {narrative_summary}
Known characters: {', '.join(character_list)}

For each subject, provide:
1. Unique identifier (camelCase)
2. Exact visual description phrase
3. Category (character/object/environment)
4. Relationships with other subjects

Respond with valid JSON only:"""
    
    # Add schema
    schema = self._get_json_schema()
    
    # Check token count and adjust if needed
    full_prompt = f"{base_prompt}\n{json.dumps(schema, indent=2)}"
    
    if self._estimate_tokens(full_prompt) > 1500:  # Conservative limit
        # Use simplified schema
        simplified_schema = self._get_simplified_schema()
        full_prompt = f"{base_prompt}\n{json.dumps(simplified_schema, indent=2)}"
    
    return full_prompt

def _build_narrative_summary(self, scenes: List[Dict]) -> str:
    """Create concise narrative summary from scenes."""
    summary_parts = []
    
    for scene in scenes[:3]:  # Limit to first 3 scenes for token management
        location = scene.get("location", "")
        action_blocks = scene.get("action_blocks", [])
        
        # Take first 2 action blocks per scene
        key_actions = action_blocks[:2]
        scene_summary = f"{location}: {' '.join(key_actions)}"
        summary_parts.append(scene_summary)
    
    return " | ".join(summary_parts)
```

### 3. Retry Logic with Fallbacks
```python
def extract_subjects_with_retry(self, screenplay_data: Dict) -> Dict:
    """Extract subjects with retry logic for incomplete responses."""
    
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            # Generate prompt (simplified on retries)
            if attempt == 0:
                prompt = self.create_extraction_prompt(screenplay_data)
            else:
                prompt = self.create_simplified_prompt(screenplay_data, attempt)
            
            # Call LLM
            response = self.llm.invoke(prompt)
            response_text = response.content if hasattr(response, 'content') else str(response)
            
            # Validate and repair JSON
            result = self.validate_and_repair_json(response_text)
            
            if result and self._validate_required_fields(result):
                # Add metadata
                result["analysis"]["processing_notes"] = f"Successful extraction on attempt {attempt + 1}"
                return result
            
        except Exception as e:
            if attempt == max_retries - 1:
                # Final attempt failed, return fallback
                return self._create_fallback_response(screenplay_data, str(e))
            
            # Wait before retry (exponential backoff)
            time.sleep(2 ** attempt)
    
    return self._create_fallback_response(screenplay_data, "Max retries exceeded")

def create_simplified_prompt(self, screenplay_data: Dict, attempt: int) -> str:
    """Create increasingly simplified prompts for retries."""
    
    if attempt == 1:
        # Retry 1: Shorter narrative, basic schema
        return f"""Extract main subjects from: {self._get_key_narrative(screenplay_data)}

JSON format:
{{
  "subjects": [
    {{
      "identifier": "string",
      "exact_phrase": "string", 
      "category": "character|object|environment"
    }}
  ]
}}"""
    
    elif attempt == 2:
        # Retry 2: Minimal prompt
        return f"""List visual subjects from: {self._get_minimal_narrative(screenplay_data)}

Format: {{"subjects": [{{"identifier": "name", "exact_phrase": "description", "category": "type"}}]}}"""
```

### 4. Fallback Response Generation
```python
def _create_fallback_response(self, screenplay_data: Dict, error: str) -> Dict:
    """Create fallback response when LLM fails."""
    
    # Extract basic subjects from screenplay data
    characters = screenplay_data.get("characters", [])
    fallback_subjects = []
    
    for char in characters:
        subject = {
            "identifier": char["name"].replace(" ", ""),
            "exact_phrase": f"character named {char['name']}",
            "category": "character",
            "relationships": [],
            "text_references": char.get("mentions", [])[:2],
            "confidence": 0.5,
            "extraction_notes": "Fallback extraction from parser data"
        }
        fallback_subjects.append(subject)
    
    return {
        "subjects": fallback_subjects,
        "analysis": {
            "subject_count": len(fallback_subjects),
            "ambiguities": [{"subject": "all", "issue": "LLM extraction failed, using fallback"}],
            "processing_notes": f"Fallback response due to: {error}",
            "token_usage": {"prompt_tokens": 0, "response_tokens": 0, "total_tokens": 0}
        }
    }
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
import re
import time
from typing import Dict, List

class SubjectExtractor(Component):
    display_name = "002 Subject Extractor"
    description = "Extract subjects with robust JSON validation and retry logic"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Extracted Subjects", name="extracted_subjects", method="extract_subjects")
    ]
    
    def extract_subjects(self) -> Data:
        """Main extraction method with error handling."""
        try:
            screenplay_data = self.parsed_screenplay.data
            
            if not screenplay_data or "scenes" not in screenplay_data:
                return Data(data=self._create_error_response("Invalid screenplay data"))
            
            # Extract subjects with retry logic
            result = self.extract_subjects_with_retry(screenplay_data)
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Extraction failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm`

**Output:** `extracted_subjects` → Description Enhancer

### Testing Strategy
```python
# Test with sample data
test_screenplay = {
    "scenes": [
        {
            "scene_number": 1,
            "location": "Grandmother's House",
            "action_blocks": [
                "Little Red Riding Hood enters with basket",
                "She sees grandmother in bed"
            ]
        }
    ],
    "characters": [
        {"name": "Little Red Riding Hood", "mentions": ["enters with basket"]},
        {"name": "Grandmother", "mentions": ["grandmother in bed"]}
    ]
}

# Expected: Complete JSON with all subjects extracted
# No truncated responses like "possession"...
```

## Benefits
- ✅ **Fixes truncation issue** - Auto-repair for incomplete JSON
- ✅ **Robust error handling** - Retry logic and fallbacks
- ✅ **Token management** - Smart prompt sizing
- ✅ **Isolated failures** - Doesn't break the chain
- ✅ **Quality validation** - Ensures complete responses

## Next Steps
1. Implement after Screenplay Parser
2. Test with your Gemini API
3. Monitor for complete JSON responses
4. Verify no more "possession"... truncations
5. Connect to Description Enhancer
