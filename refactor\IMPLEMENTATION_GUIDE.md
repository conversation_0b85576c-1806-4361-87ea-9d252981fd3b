# Implementation Guide

## Step-by-Step Implementation Plan

### Phase 1: Foundation (Start Here)

#### 1.1 Create Utility Functions
**Priority: CRITICAL** - These handle the incomplete JSON issue

**Files to create:**
- `utils/json_validator.py` - Handles incomplete JSON responses
- `utils/llm_helper.py` - Robust LLM calling with retries
- `utils/prompt_templates.py` - Structured prompt generation

**Why this fixes your issue:**
Your "possession"... truncation happens because:
1. Gemini 2.5 free tier hits token limits
2. No validation/retry logic exists
3. Incomplete JSON breaks the entire chain

**Solution:**
```python
# Example of how json_validator.py will fix this:
incomplete_json = '{"related_subject": "Cake", "relationship_type": "possession"...'
repaired_json = JSONValidator.repair_incomplete_json(incomplete_json)
# Result: Complete, valid JSON with proper closing brackets
```

#### 1.2 Create First Component
**File:** `001-screenplay-parser.py`
**Type:** Pure Python (no LLM calls)
**Purpose:** Parse screenplay text into structured data

**Why start here:**
- No LLM dependencies = no token issues
- Tests your Python component setup
- Provides clean data for subsequent components

### Phase 2: LLM Integration (Test with Your Gemini API)

#### 2.1 Subject Extractor
**File:** `002-subject-extractor.py`
**Type:** Hybrid (Python + 1 LLM call)
**Key features:**
- JSON validation with auto-repair
- Retry logic for incomplete responses
- Fallback to simplified prompts if needed

**Testing strategy:**
1. Test with your existing narrative text
2. Monitor for incomplete responses
3. Verify JSON repair functionality
4. Check token usage vs. old workflow

#### 2.2 Description Enhancer
**File:** `003-description-enhancer.py`
**Type:** Hybrid (Python + 1 LLM call)
**Key features:**
- Progressive enhancement (basic → detailed)
- Smart prompt chunking for token limits
- Validation of enhanced descriptions

### Phase 3: Advanced Components

#### 3.1 Shot Generator
**File:** `004-shot-generator.py`
**Type:** Hybrid (Python + 1 LLM call)
**Purpose:** AI-driven cinematic shot creation
**Note:** This needs the most AI creativity, so LLM usage is justified

#### 3.2 Scene Descriptor
**File:** `005-scene-descriptor.py`
**Type:** Mostly Python with minimal LLM
**Purpose:** Combine data into coherent scene descriptions

#### 3.3 Consistency Manager
**File:** `006-consistency-manager.py`
**Type:** Pure Python
**Purpose:** Ensure subject consistency across prompts

#### 3.4 VEO3 Optimizer
**File:** `007-veo3-optimizer.py`
**Type:** Hybrid (Python + 1 LLM call)
**Purpose:** Final optimization for VEO3 video generation

## Langflow Connection Instructions

### Current Problematic Setup:
```
File → Prompt Template 1 → Language Model 1 → 
Prompt Template 2 → Language Model 2 → 
Prompt Template 3 → Language Model 3 → 
Prompt Template 4 → Language Model 4 → 
Prompt Template 5 → Language Model 5
```

### New Efficient Setup:
```
File → Python Component (Screenplay Parser) → 
Python Component (Subject Extractor) → Language Model (Gemini) → 
Python Component (Description Enhancer) → Language Model (Gemini) → 
Python Component (Shot Generator) → Language Model (Gemini) → 
Python Component (Scene Descriptor) → 
Python Component (Consistency Manager) → 
Python Component (VEO3 Optimizer) → Language Model (Gemini)
```

## Component Connection Details

### 1. Screenplay Parser
**Input:** File component (your narrative text)
**Output:** Structured data (scenes, characters, action blocks)
**Connection:** Direct to Subject Extractor

### 2. Subject Extractor
**Inputs:** 
- Screenplay data (from parser)
- Language Model (Gemini 2.5)
**Output:** Extracted subjects with validation
**Connection:** To Description Enhancer

### 3. Description Enhancer
**Inputs:**
- Subject data (from extractor)
- Language Model (Gemini 2.5)
**Output:** Enhanced subject descriptions
**Connection:** To Shot Generator

### 4. Shot Generator
**Inputs:**
- Enhanced subjects
- Language Model (Gemini 2.5)
**Output:** Cinematic shots
**Connection:** To Scene Descriptor

### 5. Scene Descriptor
**Input:** Shot data
**Output:** Scene descriptions
**Connection:** To Consistency Manager

### 6. Consistency Manager
**Input:** Scene descriptions
**Output:** Consistent prompts
**Connection:** To VEO3 Optimizer

### 7. VEO3 Optimizer
**Inputs:**
- Consistent prompts
- Language Model (Gemini 2.5)
**Output:** Final VEO3-optimized prompts

## Testing Strategy

### 1. Component-by-Component Testing
Test each component individually before connecting:
```bash
# Test screenplay parser
python test_screenplay_parser.py

# Test subject extractor with sample data
python test_subject_extractor.py

# etc.
```

### 2. Integration Testing
Connect components one by one:
1. File → Screenplay Parser
2. Add Subject Extractor
3. Add Description Enhancer
4. Continue incrementally

### 3. Token Usage Monitoring
Track token usage at each LLM call:
- Old workflow: ~5000+ tokens across 5 calls
- New workflow: ~2000-3000 tokens across 3-4 calls

### 4. JSON Validation Testing
Test with intentionally incomplete responses:
- Simulate token limit cutoffs
- Verify auto-repair functionality
- Test retry mechanisms

## Error Handling Strategy

### 1. JSON Validation Errors
```python
# If JSON is incomplete:
if not JSONValidator.is_valid_json(response):
    # Try to repair
    repaired = JSONValidator.repair_incomplete_json(response)
    if repaired:
        return repaired
    else:
        # Retry with simplified prompt
        return retry_with_fallback()
```

### 2. LLM API Errors
```python
# If API call fails:
try:
    response = llm.call(prompt)
except Exception as e:
    # Log error and retry
    logger.error(f"LLM call failed: {e}")
    return retry_with_exponential_backoff()
```

### 3. Token Limit Errors
```python
# If prompt too long:
if len(prompt) > token_limit:
    # Split into chunks or simplify
    simplified_prompt = create_simplified_prompt(data)
    return llm.call(simplified_prompt)
```

## Success Metrics

### Before (Current Issues):
- 5 LLM calls in sequence
- ~60% incomplete JSON responses
- High token usage
- Error propagation through chain

### After (Target Goals):
- 3-4 strategic LLM calls
- <5% incomplete responses (with auto-repair)
- 40-50% reduction in token usage
- Isolated error handling per component

## Next Steps
1. Review component specifications in `COMPONENT_SPECS.md`
2. Check Langflow connection details in `LANGFLOW_INSTRUCTIONS.md`
3. Start with Phase 1 implementation
4. Test incrementally with your Gemini API
