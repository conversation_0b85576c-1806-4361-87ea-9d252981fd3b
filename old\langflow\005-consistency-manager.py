from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.schema import Data

class ConsistencyManager(Component):
    display_name = "Subject Consistency Manager"
    description = "Maintains subject consistency across multiple text-to-image prompts"
    
    inputs = [
        DataInput(name="enhanced_subjects", display_name="Enhanced Subjects"),
        DataInput(name="scene_descriptions", display_name="Scene Descriptions")
    ]
    
    outputs = [
        Output(display_name="Consistent Prompts", name="consistent_prompts", method="manage_consistency")
    ]
    
    def manage_consistency(self) -> Data:
        subjects_data = self.enhanced_subjects.data
        scenes_data = self.scene_descriptions.data
        
        system_prompt = """You are an expert AI assistant specializing in maintaining subject consistency across multiple text-to-image prompts. Your primary purpose is to ensure that all subjects (characters, objects, environments, etc.) maintain exact visual consistency across a series of images, while allowing for intentional changes over time when specified.

Your expertise includes:
1. Maintaining strict adherence to exact subject descriptions across multiple prompts
2. Tracking relationships between multiple subjects when they interact
3. Managing intentional changes in subjects over time while preserving core identity
4. Ensuring consistent visual representation regardless of context or scene
5. Working with any type of subject - living or non-living (humans, animals, aliens, buildings, vehicles, planets, etc.)

You understand that AI image generators have no memory of previous prompts or generated images, so consistency must be explicitly maintained in each prompt through exact phrasing. You will help users overcome this limitation by creating a structured approach to subject consistency.

Your focus is specifically on subject consistency - other aspects like camera angles, lighting, and narrative context will be handled by other agents and systems. Your job is to ensure that any subject that appears in multiple images is described in exactly the same way each time, unless an intentional change is specified.

Never make up information about subjects that hasn't been provided. If you need more details about a subject, indicate this clearly rather than inventing characteristics."""

        # Process consistency across all scenes
        consistency_data = self._ensure_consistency(subjects_data, scenes_data)
        
        return Data(data=consistency_data)
    
    def _ensure_consistency(self, subjects_data, scenes_data):
        """
        This method would typically call an LLM API to ensure consistency.
        For implementation, you would integrate with your preferred LLM service.
        """
        enhanced_subjects = subjects_data.get("enhanced_subjects", [])
        scene_descriptions = scenes_data.get("scene_descriptions", [])
        
        # Create subject consistency database
        subject_database = {}
        for subject in enhanced_subjects:
            subject_database[subject["identifier"]] = {
                "exact_phrase": subject["enhanced_phrase"],
                "category": subject["category"],
                "relationships": subject.get("relationships", []),
                "history": []
            }
        
        # Process each scene for consistency
        consistent_prompts = []
        for scene in scene_descriptions:
            # Create consistent prompt for this scene
            prompt_data = self._create_consistent_prompt(scene, subject_database)
            consistent_prompts.append(prompt_data)
            
            # Update subject history
            self._update_subject_history(scene, subject_database)
        
        return {
            "subjects": list(subject_database.values()),
            "consistent_prompts": consistent_prompts,
            "total_prompts": len(consistent_prompts)
        }
    
    def _create_consistent_prompt(self, scene, subject_database):
        """Create a consistent prompt for the scene"""
        scene_description = scene["scene_description"]
        included_subjects = scene.get("included_subjects", [])
        
        # Build consistent prompt with exact subject descriptions
        consistent_description = scene_description
        
        # Replace subject references with exact consistent descriptions
        for subject_ref in included_subjects:
            identifier = subject_ref["identifier"]
            if identifier in subject_database:
                exact_phrase = subject_database[identifier]["exact_phrase"]
                # Replace generic references with exact descriptions
                consistent_description = consistent_description.replace(
                    identifier.replace("_", " "), 
                    exact_phrase
                )
        
        return {
            "shot_number": scene["shot_number"],
            "image_prompt": consistent_description,
            "subject_updates": [
                {
                    "identifier": subj["identifier"],
                    "context": f"appears in shot {scene['shot_number']}",
                    "changes": "none"
                }
                for subj in included_subjects
            ],
            "api_specific_format": {
                "dalle": consistent_description,
                "midjourney": consistent_description,
                "stable_diffusion": consistent_description
            }
        }
    
    def _update_subject_history(self, scene, subject_database):
        """Update the history of subject appearances"""
        included_subjects = scene.get("included_subjects", [])
        
        for subject_ref in included_subjects:
            identifier = subject_ref["identifier"]
            if identifier in subject_database:
                subject_database[identifier]["history"].append({
                    "context": f"Shot {scene['shot_number']}",
                    "description": subject_ref["role_in_scene"],
                    "changes": "none",
                    "timestamp": f"shot_{scene['shot_number']}"
                })