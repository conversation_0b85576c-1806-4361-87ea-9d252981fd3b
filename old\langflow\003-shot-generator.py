from langflow.custom import Component
from langflow.io import DataInput, MessageTextInput, DropdownInput, Output
from langflow.schema import Data

class ShotGenerator(Component):
    display_name = "Cinematic Shot Generator"
    description = "AI-powered shot generation with professional cinematography"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        DataInput(name="enhanced_subjects", display_name="Enhanced Subjects"),
        MessageTextInput(name="shot_duration", display_name="Default Shot Duration", value="8 seconds"),
        DropdownInput(name="genre", display_name="Genre", 
                     options=["drama", "comedy", "thriller", "horror", "action", "romance"], value="drama")
    ]
    
    outputs = [
        Output(display_name="Generated Shots", name="shots", method="generate_shots")
    ]
    
    def generate_shots(self) -> Data:
        screenplay_data = self.parsed_screenplay.data
        subjects_data = self.enhanced_subjects.data
        duration = self.shot_duration or "8 seconds"
        genre = self.genre or "drama"
        
        system_prompt = f"""You are a professional cinematographer and director specializing in creating detailed shot lists for {genre} productions. Your expertise includes:

1. Breaking down scenes into individual, cinematic shots
2. Determining appropriate camera angles, movements, and framing based on story context
3. Applying genre-specific cinematographic conventions
4. Creating shots that serve the narrative and emotional beats
5. Ensuring proper shot progression and visual flow

For each shot, consider:
- **Narrative function**: What story purpose does this moment serve?
- **Emotional context**: What should the audience feel?
- **Character dynamics**: Power relationships, intimacy, conflict
- **Visual storytelling**: How can the camera enhance the story?
- **Genre conventions**: What cinematographic style fits the genre?

Generate shots that are:
- Visually specific and detailed
- Cinematographically sound
- Appropriate for the narrative context
- Suitable for AI video generation"""

        # Generate shots using AI analysis
        shots_data = self._generate_cinematic_shots(screenplay_data, subjects_data, duration, genre)
        
        return Data(data=shots_data)
    
    def _generate_cinematic_shots(self, screenplay_data, subjects_data, duration, genre):
        """
        This method would typically call an LLM API for intelligent shot generation.
        For implementation, you would integrate with your preferred LLM service.
        """
        shots = []
        shot_number = 1
        
        action_blocks = screenplay_data.get("action_blocks", [])
        characters = screenplay_data.get("characters", [])
        enhanced_subjects = subjects_data.get("enhanced_subjects", [])
        
        for block in action_blocks:
            if len(block.strip()) > 10:
                # AI would analyze each block and create appropriate shots
                # This is a simplified version - replace with actual LLM call
                
                # Determine shot characteristics based on content and genre
                shot_data = {
                    "shot_number": shot_number,
                    "duration": duration,
                    "scene_context": self._extract_scene_context(block),
                    "description": self._create_shot_description(block, genre),
                    "camera_work": self._determine_camera_work(block, genre),
                    "characters_present": self._identify_characters_in_shot(block, characters),
                    "visual_elements": self._extract_visual_elements(block, enhanced_subjects)
                }
                
                shots.append(shot_data)
                shot_number += 1
        
        return {"shots": shots, "total_shots": len(shots)}
    
    def _extract_scene_context(self, block):
        """Extract scene location, time, mood from the block"""
        # Placeholder - would use AI to analyze context
        return {
            "location": "unknown",
            "time_of_day": "unknown",
            "mood": "neutral"
        }
    
    def _create_shot_description(self, block, genre):
        """Create a detailed shot description suitable for video generation"""
        # Placeholder - would use AI to create professional description
        return f"Cinematic {genre} shot: {block[:100]}..."
    
    def _determine_camera_work(self, block, genre):
        """Determine appropriate camera angle, movement, framing"""
        # Placeholder - would use AI to determine cinematography
        return {
            "angle": "medium shot",
            "movement": "static",
            "framing": "standard"
        }
    
    def _identify_characters_in_shot(self, block, characters):
        """Identify which characters are present in this specific shot"""
        present_characters = []
        for char in characters:
            if char.upper() in block.upper():
                present_characters.append(char)
        return present_characters
    
    def _extract_visual_elements(self, block, enhanced_subjects):
        """Extract relevant visual elements from enhanced subjects"""
        relevant_elements = []
        for subject in enhanced_subjects:
            if any(ref.lower() in block.lower() for ref in subject.get("text_references", [])):
                relevant_elements.append({
                    "identifier": subject["identifier"],
                    "description": subject["enhanced_phrase"]
                })
        return relevant_elements