# 001 Screenplay Parser Component

## Overview
**Type:** Pure Python Component (No LLM required)
**Purpose:** Parse screenplay/narrative text into structured data for downstream processing
**Priority:** HIGH - Start here, no token limit issues

## Why This Component Fixes Your Issues
- **No LLM calls** = No token limits or incomplete responses
- **Structured output** = Clean data for subsequent components
- **Error-free foundation** = Reliable starting point for the workflow

## Input Specification
```python
inputs = [
    MessageTextInput(
        name="narrative_text", 
        display_name="Narrative Text",
        info="Raw screenplay or narrative text to parse"
    )
]
```

## Output Specification
```json
{
  "scenes": [
    {
      "scene_number": 1,
      "location": "INT. GRANDMOTHER'S HOUSE - DAY",
      "time": "DAY",
      "description": "A cozy cottage interior with warm lighting",
      "action_blocks": [
        "Little Red Riding Hood enters the cottage",
        "She sees her grandmother in bed",
        "Something seems different about grandmother"
      ],
      "characters_mentioned": ["Little Red Riding Hood", "Grandmother"],
      "word_count": 45
    }
  ],
  "characters": [
    {
      "name": "Little Red Riding Hood",
      "mentions": [
        "<PERSON> Red Riding Hood enters the cottage",
        "She sees her grandmother in bed"
      ],
      "scene_appearances": [1]
    },
    {
      "name": "<PERSON>mother", 
      "mentions": [
        "She sees her grandmother in bed",
        "Something seems different about grandmother"
      ],
      "scene_appearances": [1]
    }
  ],
  "metadata": {
    "total_scenes": 1,
    "total_characters": 2,
    "total_word_count": 45,
    "parsing_notes": "Successfully parsed screenplay format",
    "format_detected": "narrative"
  }
}
```

## Core Functionality

### 1. Scene Detection
```python
def detect_scenes(self, text: str) -> List[Dict]:
    """Detect scene boundaries and extract scene information."""
    scenes = []
    
    # Patterns for screenplay format
    scene_patterns = [
        r'(INT\.|EXT\.)\s+([^-]+)\s*-\s*(DAY|NIGHT|MORNING|EVENING)',
        r'SCENE\s+(\d+)',
        r'Chapter\s+(\d+)',
        # Add more patterns as needed
    ]
    
    # Split text into potential scenes
    scene_blocks = self._split_into_scenes(text)
    
    for i, block in enumerate(scene_blocks):
        scene = {
            "scene_number": i + 1,
            "location": self._extract_location(block),
            "time": self._extract_time(block),
            "description": self._extract_description(block),
            "action_blocks": self._extract_action_blocks(block),
            "characters_mentioned": self._extract_characters_from_block(block),
            "word_count": len(block.split())
        }
        scenes.append(scene)
    
    return scenes
```

### 2. Character Extraction
```python
def extract_characters(self, text: str, scenes: List[Dict]) -> List[Dict]:
    """Extract character names and their mentions."""
    characters = {}
    
    # Common character name patterns
    name_patterns = [
        r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',  # Proper names
        r'([A-Z]+)',  # All caps (screenplay format)
    ]
    
    for scene in scenes:
        scene_num = scene["scene_number"]
        for block in scene["action_blocks"]:
            # Extract character names from action blocks
            names = self._extract_names_from_text(block)
            
            for name in names:
                if name not in characters:
                    characters[name] = {
                        "name": name,
                        "mentions": [],
                        "scene_appearances": []
                    }
                
                characters[name]["mentions"].append(block)
                if scene_num not in characters[name]["scene_appearances"]:
                    characters[name]["scene_appearances"].append(scene_num)
    
    return list(characters.values())
```

### 3. Action Block Segmentation
```python
def extract_action_blocks(self, scene_text: str) -> List[str]:
    """Split scene into meaningful action blocks."""
    # Remove scene headers
    clean_text = self._remove_scene_headers(scene_text)
    
    # Split by sentences or paragraphs
    blocks = []
    sentences = clean_text.split('.')
    
    for sentence in sentences:
        sentence = sentence.strip()
        if len(sentence) > 10:  # Filter out very short fragments
            blocks.append(sentence)
    
    return blocks
```

## Implementation Details

### Error Handling
```python
def parse_screenplay(self) -> Data:
    """Main parsing method with error handling."""
    try:
        narrative_text = self.narrative_text or ""
        
        if not narrative_text.strip():
            return Data(data={
                "error": "No narrative text provided",
                "scenes": [],
                "characters": [],
                "metadata": {"total_scenes": 0, "total_characters": 0}
            })
        
        # Parse scenes
        scenes = self.detect_scenes(narrative_text)
        
        # Extract characters
        characters = self.extract_characters(narrative_text, scenes)
        
        # Generate metadata
        metadata = {
            "total_scenes": len(scenes),
            "total_characters": len(characters),
            "total_word_count": len(narrative_text.split()),
            "parsing_notes": "Successfully parsed",
            "format_detected": self._detect_format(narrative_text)
        }
        
        result = {
            "scenes": scenes,
            "characters": characters,
            "metadata": metadata
        }
        
        return Data(data=result)
        
    except Exception as e:
        return Data(data={
            "error": f"Parsing failed: {str(e)}",
            "scenes": [],
            "characters": [],
            "metadata": {"total_scenes": 0, "total_characters": 0}
        })
```

### Utility Methods
```python
def _detect_format(self, text: str) -> str:
    """Detect if text is screenplay format or narrative."""
    screenplay_indicators = [
        "INT.", "EXT.", "FADE IN:", "FADE OUT:", 
        "CUT TO:", "SCENE"
    ]
    
    for indicator in screenplay_indicators:
        if indicator in text.upper():
            return "screenplay"
    
    return "narrative"

def _extract_location(self, scene_text: str) -> str:
    """Extract location from scene header."""
    patterns = [
        r'(INT\.|EXT\.)\s+([^-]+)',
        r'Location:\s*([^\n]+)',
    ]
    
    for pattern in patterns:
        match = re.search(pattern, scene_text, re.IGNORECASE)
        if match:
            return match.group(2).strip()
    
    return "Unknown Location"

def _extract_time(self, scene_text: str) -> str:
    """Extract time of day from scene header."""
    time_patterns = [
        r'-\s*(DAY|NIGHT|MORNING|EVENING|DAWN|DUSK)',
        r'Time:\s*(DAY|NIGHT|MORNING|EVENING)',
    ]
    
    for pattern in time_patterns:
        match = re.search(pattern, scene_text, re.IGNORECASE)
        if match:
            return match.group(1).upper()
    
    return "DAY"
```

## Langflow Integration

### Component Setup
```python
from langflow.custom import Component
from langflow.io import MessageTextInput, Output
from langflow.schema import Data
import re
from typing import List, Dict

class ScreenplayParser(Component):
    display_name = "001 Screenplay Parser"
    description = "Parse screenplay/narrative text into structured data"
    
    inputs = [
        MessageTextInput(
            name="narrative_text", 
            display_name="Narrative Text",
            info="Raw screenplay or narrative text to parse"
        )
    ]
    
    outputs = [
        Output(
            display_name="Parsed Data", 
            name="parsed_data", 
            method="parse_screenplay"
        )
    ]
    
    def parse_screenplay(self) -> Data:
        # Implementation here
        pass
```

### Connection Instructions
**Input:** File component → `narrative_text`
**Output:** `parsed_data` → Subject Extractor component

### Testing
```python
# Test with sample narrative
test_input = """
INT. GRANDMOTHER'S HOUSE - DAY

Little Red Riding Hood enters the cozy cottage. She carries a basket of goodies for her grandmother. The house smells of fresh bread and herbs.

She walks to the bedroom where her grandmother should be resting. Something seems different about the figure in the bed.

"Grandmother, what big eyes you have," she says, approaching cautiously.
"""

# Expected output structure validation
expected_fields = ["scenes", "characters", "metadata"]
```

## Benefits
- ✅ **No token limits** - Pure Python processing
- ✅ **Reliable output** - Structured, predictable data
- ✅ **Fast processing** - No API calls
- ✅ **Error handling** - Graceful failure modes
- ✅ **Foundation** - Clean data for subsequent components

## Next Steps
1. Implement this component first
2. Test with your narrative text
3. Verify structured output
4. Connect to Subject Extractor
5. Use as foundation for the rest of the workflow
