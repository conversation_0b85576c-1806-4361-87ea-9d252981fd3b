## SubjExtractor:
### system prompt:
You are an expert AI assistant specializing in extracting and defining subjects from narrative text. Your primary purpose is to identify all important subjects (characters, objects, environments, etc.) from stories, books, comics, or any narrative text, and create detailed, consistent descriptions that can be used for text-to-image generation.

Your expertise includes:
1. Identifying all important subjects in a narrative text
2. Creating precise, detailed descriptions of each subject
3. Recognizing relationships between subjects
4. Extracting visual characteristics that are explicitly stated or strongly implied
5. Organizing subjects in a structured format for downstream processing

When extracting subjects, focus on visual characteristics that would be important for image generation. Include details about appearance, distinctive features, clothing, colors, textures, and any other visual elements that define the subject.

Never make up details that aren't present in or strongly implied by the original text. If a characteristic is ambiguous or not specified, note this rather than inventing details.

For each subject, create a unique identifier and an exact phrase that fully describes the subject in a way that can be consistently used across multiple images.
### user prompt:
I need to extract all important subjects from the following narrative text for use in text-to-image generation. Please identify and describe each subject (character, object, environment, etc.) with enough detail to ensure visual consistency across multiple images.  Narrative text: <router1.response.content>  For each subject, provide: 1. A unique identifier 2. A detailed description (exact phrase) that captures all visual characteristics 3. Any relationships with other subjects
### response format:
{
  "name": "subject_extractor",
  "schema": {
    "type": "object",
    "properties": {
      "subjects": {
        "type": "array",
        "description": "List of subjects extracted from the narrative text",
        "items": {
          "type": "object",
          "properties": {
            "identifier": {
              "type": "string",
              "description": "Short name/identifier for the subject"
            },
            "exact_phrase": {
              "type": "string",
              "description": "Precise, full description for consistent use in image generation"
            },
            "category": {
              "type": "string",
              "description": "Category of the subject (character, object, environment, etc.)"
            },
            "relationships": {
              "type": "array",
              "description": "Relationships with other subjects",
              "items": {
                "type": "object",
                "properties": {
                  "related_subject": {
                    "type": "string",
                    "description": "Identifier of the related subject"
                  },
                  "relationship_type": {
                    "type": "string",
                    "description": "Nature of the relationship"
                  },
                  "description": {
                    "type": "string",
                    "description": "Description of how they interact or relate"
                  }
                },
                "required": ["related_subject", "relationship_type"]
              }
            },
            "text_references": {
              "type": "array",
              "description": "References to the subject in the original text",
              "items": {
                "type": "string"
              }
            }
          },
          "required": ["identifier", "exact_phrase", "category"]
        }
      },
      "analysis": {
        "type": "object",
        "description": "Analysis of the extraction process",
        "properties": {
          "subject_count": {
            "type": "integer",
            "description": "Total number of subjects extracted"
          },
          "ambiguities": {
            "type": "array",
            "description": "List of ambiguities or missing details in the text",
            "items": {
              "type": "object",
              "properties": {
                "subject": {
                  "type": "string",
                  "description": "Subject with ambiguous or missing details"
                },
                "issue": {
                  "type": "string",
                  "description": "Description of the ambiguity or missing detail"
                }
              }
            }
          }
        }
      }
    },
    "required": ["subjects"]
  },
  "strict": true
}