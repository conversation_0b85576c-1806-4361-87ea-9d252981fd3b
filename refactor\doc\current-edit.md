also, the "1. Unique identifier (camelCase)" is probably wrong, the simstudio-router2imageprompts folder contained block content that belonged to simstudio, i have migrated to langflow and i prefer standard industry always plus I want to adhere to langflow docs if that make sense. 
---
as well as here for example:     for scene in scenes[:3]:  # Limit to first 3 scenes for token management
... well i dont want any limitations! i WANT A COMPLETE FUCKING MOVIE. I CAN GO AND GET THE GEMINI FLASH 2.5 PAID MODEL if thats so urgent!. the workflow should be able to produce hundreds or even thousands of fully working text to video prompts if the story/screenplay is long. regarding the video duration, Users set The duration Due to different model selection later in the future. And since the VEO  3 Generates eight seconds long videos only Then I thought the that I could prepare the duration string for the user to set manually.

inside the zero zero two subject extractor.py I see extraction of characters, object, and environments. However, what I would rather like to have is separate extraction of characters separate extraction of objects, separate extraction of environments, and separate extractions of anything else needed (This will ease up the complications in the future. That's what I like having well organized workflow. You must also definitely describe what is LLM for every .py used for and what is the llm specifically doing inside that specific component, this must be written in the components_specs.md. I definitely prefer to use LLMs just for actions that are 100% needed and necessary However, if they don't need to be included, then completely avoid them.  

for example this is a perfect example of what llm should be for:
"You are a Director of Photography. Given the following scene data, your job is to add technical and artistic direction. Respond ONLY with a JSON object containing keys for 'camera_shots', 'lighting_description', and 'sound_design'. 
Scene Data:
```
{
  "setting": "INT. GRANDMA'S COTTAGE - NIGHT",
  "action": "Little Red Riding Hood enters. A figure is in the bed.",
  "dialogue": "Grandma, what big eyes you have! / All the better to see you with, my dear."
}
```"
- The LLM's Output: A small, structured JSON object with the new, creative information.
---
i also know there is somewhere written that the workflow should have detail quality selection, this is wrong because the workflow must always produce the best output possible.
---
and lastly regarding the "Should I replace "image generation" with "storyboard generation" throughout all files?
Should I also update related terms like:
"text-to-image" → "text-to-storyboard"
"image prompts" → "storyboard prompts"
"visual consistency across multiple images" → "visual consistency across storyboard sequences"
Should I keep the VEO3 references since you're ultimately generating video prompts, or should those also be updated to reflect the storyboard-to-video workflow?
Any specific storyboard terminology you want me to emphasize (like "key frames", "camera angles", "shot sequences", etc.)"

1. yes.
2. yes.
3. yes its a storyboard-to-video workflow but currently am not gonna integrate image generators nor image detections, this will be introduces later on however i do need images to be reffered as storyboards.

- this is the correct workflow: text to image > image to video > video to audio. the simstudio-router2imageprompts is containing text to image workflow but i am leveraging it i