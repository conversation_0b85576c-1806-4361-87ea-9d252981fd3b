# Langflow Prompt Generator Refactor

## Overview
This refactor addresses the incomplete JSON output issues in the original Langflow workflow by replacing LLM chains with hybrid Python components that provide robust error handling and efficient processing.

## Problem Analysis
The original workflow had these issues:
- **5 sequential LLM calls** causing token accumulation and limits
- **Incomplete JSON responses** due to Gemini 2.5 free tier limitations
- **Error propagation** through the chain when one component fails
- **Inefficient token usage** with redundant processing

## Solution Strategy
Replace the 5-LLM chain with **hybrid Python components** that:
- Handle data processing and validation in Python
- Use LLMs only for creative/analytical tasks
- Implement robust error handling and JSON repair
- Provide fallback mechanisms for incomplete responses

## New Architecture

### Current Problematic Flow:
```
File → PT1 → LM1 → PT2 → LM2 → PT3 → LM3 → PT4 → LM4 → PT5 → LM5
```

### New Efficient Flow:
```
File → Screenplay Parser (Python) → Subject Extractor (Python+LLM) → Description Enhancer (Python+LLM) → Shot Generator (Python+LLM) → Scene Descriptor (Python) → Consistency Manager (Python) → VEO3 Optimizer (Python+LLM)
```

## Components Overview

| Component | Type | LLM Usage | Purpose |
|-----------|------|-----------|---------|
| 001-screenplay-parser | Pure Python | None | Parse screenplay into structured data |
| 002-subject-extractor | Hybrid | 1 LLM call | Extract subjects with AI + validation |
| 003-description-enhancer | Hybrid | 1 LLM call | Enhance descriptions with fallbacks |
| 004-shot-generator | Hybrid | 1 LLM call | Generate cinematic shots (AI creativity) |
| 005-scene-descriptor | Python | None | Combine data into scene descriptions |
| 006-consistency-manager | Pure Python | None | Ensure subject consistency |
| 007-veo3-optimizer | Hybrid | 1 LLM call | Final optimization for VEO3 |

## Key Features
- **Reduced LLM calls**: From 5 to 3-4 strategic calls
- **JSON validation**: Automatic repair of incomplete responses
- **Error handling**: Retry logic and fallback mechanisms
- **Token management**: Efficient prompt generation
- **Progressive enhancement**: Build complexity gradually

## Implementation Order
1. **Phase 1**: Core utilities and parser (no LLM dependencies)
2. **Phase 2**: Subject extraction with robust LLM integration
3. **Phase 3**: Description enhancement and validation
4. **Phase 4**: Advanced components (shots, scenes, consistency)
5. **Phase 5**: Final optimization and testing

## File Structure
```
refactor/
├── README.md (this file)
├── IMPLEMENTATION_GUIDE.md
├── COMPONENT_SPECS.md
├── LANGFLOW_INSTRUCTIONS.md
├── components/
│   ├── 001-screenplay-parser.md
│   ├── 002-subject-extractor.md
│   ├── 003-description-enhancer.md
│   ├── 004-shot-generator.md
│   ├── 005-scene-descriptor.md
│   ├── 006-consistency-manager.md
│   └── 007-veo3-optimizer.md
├── utils/
│   ├── json-validator-spec.md
│   ├── llm-helper-spec.md
│   └── prompt-templates-spec.md
└── schemas/
    └── json-schemas.md
```

## Next Steps
1. Review component specifications
2. Implement utilities first
3. Build and test components incrementally
4. Update Langflow workflow
5. Test with your Gemini 2.5 API

## Benefits
- **Reliability**: No more incomplete JSON responses
- **Efficiency**: Fewer API calls, better token usage
- **Maintainability**: Clear separation of concerns
- **Scalability**: Easy to add new components or modify existing ones
- **Debugging**: Better error messages and logging
