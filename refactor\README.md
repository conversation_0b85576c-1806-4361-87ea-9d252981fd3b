# Langflow Storyboard Generator Refactor

## Overview
This refactor addresses the incomplete JSON output issues in the original Langflow workflow by replacing LLM chains with hybrid Python components that provide robust error handling and efficient processing for **complete movie storyboard generation**.

## Problem Analysis
The original workflow had these issues:
- **5 sequential LLM calls** causing token accumulation and limits
- **Incomplete JSON responses** due to Gemini 2.5 free tier limitations
- **Error propagation** through the chain when one component fails
- **Inefficient token usage** with redundant processing
- **Artificial limitations** preventing full movie processing

## Solution Strategy
Replace the 5-LLM chain with **hybrid Python components** that:
- Handle data processing and validation in Python
- Use LLMs only for creative/artistic tasks (Director of Photography decisions)
- Implement robust error handling and JSON repair
- Provide fallback mechanisms for incomplete responses
- **Support complete movies** with hundreds or thousands of storyboard prompts

## New Architecture

### Current Problematic Flow:
```
File → PT1 → LM1 → PT2 → LM2 → PT3 → LM3 → PT4 → LM4 → PT5 → LM5
```

### New Efficient Storyboard Generation Flow:
```
File → Screenplay Parser (Python) → Character Extractor (Python) → Object Extractor (Python) → Environment Extractor (Python) → Other Elements Extractor (Python) → Description Enhancer (Python+LLM) → Shot Generator (Python+LLM for Director of Photography) → Scene Descriptor (Python) → Consistency Manager (Python) → Storyboard Optimizer (Python+LLM)
```

## Components Overview

| Component | Type | LLM Usage | Purpose |
|-----------|------|-----------|---------|
| 001-screenplay-parser | Pure Python | None | Parse screenplay into structured data |
| 002-character-extractor | Mostly Python | Minimal (ambiguous cases only) | Extract characters with visual descriptions |
| 003-object-extractor | Mostly Python | Minimal (ambiguous cases only) | Extract objects and props |
| 004-environment-extractor | Mostly Python | Minimal (ambiguous cases only) | Extract locations and settings |
| 005-other-elements-extractor | Mostly Python | Minimal (ambiguous cases only) | Extract additional visual elements |
| 006-description-enhancer | Hybrid | 1 LLM call | Creative enhancement of descriptions |
| 007-shot-generator | Hybrid | 1 LLM call | Director of Photography decisions (camera, lighting, sound) |
| 008-scene-descriptor | Pure Python | None | Combine data into storyboard descriptions |
| 009-consistency-manager | Pure Python | None | Ensure visual consistency across storyboard sequences |
| 010-storyboard-optimizer | Hybrid | 1 LLM call | Final optimization for storyboard generation |

## Key Features
- **Complete movie processing**: Support for hundreds/thousands of storyboard prompts
- **Separated extraction**: Individual components for characters, objects, environments
- **Strategic LLM usage**: Only for creative tasks (Director of Photography, enhancement, optimization)
- **JSON validation**: Automatic repair of incomplete responses
- **Error handling**: Retry logic and fallback mechanisms
- **No artificial limitations**: Process entire movies, not just samples
- **Industry standard**: snake_case identifiers and Langflow best practices

## Implementation Order
1. **Phase 1**: Core utilities and screenplay parser (Pure Python)
2. **Phase 2**: Separated extraction components (Mostly Python)
3. **Phase 3**: Creative enhancement (Python + LLM for artistic decisions)
4. **Phase 4**: Director of Photography shot generation (Python + LLM for cinematography)
5. **Phase 5**: Scene description and consistency (Pure Python)
6. **Phase 6**: Final storyboard optimization (Python + LLM)

## File Structure
```
refactor/
├── README.md (this file)
├── IMPLEMENTATION_GUIDE.md
├── COMPONENT_SPECS.md
├── LANGFLOW_INSTRUCTIONS.md
├── components/
│   ├── 001-screenplay-parser.md
│   ├── 002-character-extractor.md
│   ├── 003-object-extractor.md
│   ├── 004-environment-extractor.md
│   ├── 005-other-elements-extractor.md
│   ├── 006-description-enhancer.md
│   ├── 007-shot-generator.md
│   ├── 008-scene-descriptor.md
│   ├── 009-consistency-manager.md
│   └── 010-storyboard-optimizer.md
├── utils/
│   ├── json-validator-spec.md
│   ├── llm-helper-spec.md
│   └── prompt-templates-spec.md
└── schemas/
    └── json-schemas.md
```

## Next Steps
1. Review component specifications
2. Implement utilities first
3. Build and test components incrementally
4. Update Langflow workflow
5. Test with your Gemini 2.5 API

## Benefits
- **Complete movie processing**: Generate hundreds/thousands of storyboard prompts
- **Reliability**: No more incomplete JSON responses
- **Separated concerns**: Individual extraction components for better organization
- **Strategic LLM usage**: Only for creative tasks, not data processing
- **Industry standards**: snake_case identifiers and Langflow best practices
- **No limitations**: Process entire movies without artificial constraints
- **Better storyboard quality**: Each prompt optimized for storyboard generation
