# 003 Object Extractor Component

## Overview
**Type:** Mostly Python Component (Minimal LLM usage for ambiguous cases only)
**Purpose:** Extract objects and props with visual descriptions for storyboard generation
**Priority:** HIGH - Part of the separated extraction architecture

## LLM Usage Documentation
**When LLM is used:** Only for ambiguous object descriptions that cannot be determined from text analysis
**What LLM does:** Provides creative visual details for objects with minimal description
**Why LLM is needed:** Some objects may be mentioned without visual details, requiring creative interpretation
**Token usage:** Minimal - only called for unclear cases, not for every object

## Input Specification
```python
inputs = [
    DataInput(
        name="parsed_screenplay", 
        display_name="Parsed Screenplay",
        info="Structured screenplay data from parser"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 (used only for ambiguous cases)"
    )
]
```

## Output Specification
```json
{
  "extracted_objects": [
    {
      "object_id": "wicker_basket",
      "object_name": "Wicker Basket",
      "object_category": "prop",
      "visual_description": "brown wicker basket with red checkered cloth, filled with baked goods",
      "extraction_method": "text_analysis",
      "confidence": 0.95,
      "text_references": [
        "She carries a basket of goodies",
        "basket filled with treats for grandmother"
      ],
      "scene_appearances": [1, 2],
      "object_properties": ["portable", "container", "handmade"],
      "related_characters": ["little_red_riding_hood"],
      "story_importance": "high"
    },
    {
      "object_id": "cottage_door",
      "object_name": "Cottage Door",
      "object_category": "furniture",
      "visual_description": "wooden cottage door with iron hinges and brass knocker",
      "extraction_method": "text_analysis",
      "confidence": 0.85,
      "text_references": [
        "She knocks on the cottage door",
        "The door creaks open"
      ],
      "scene_appearances": [1],
      "object_properties": ["wooden", "entrance", "rustic"],
      "related_characters": [],
      "story_importance": "medium"
    }
  ],
  "extraction_summary": {
    "total_objects": 12,
    "text_extracted": 10,
    "llm_enhanced": 2,
    "categories": {
      "props": 5,
      "furniture": 4,
      "clothing": 2,
      "food": 1
    },
    "ambiguous_cases": [
      {
        "object": "mysterious_item",
        "issue": "No visual description provided in text",
        "resolution": "LLM provided creative interpretation"
      }
    ],
    "processing_notes": "Object extraction completed successfully"
  }
}
```

## Core Functionality

### 1. Text-Based Object Extraction (Primary Method)
```python
def extract_objects_from_text(self, screenplay_data: Dict) -> List[Dict]:
    """Extract objects using Python text analysis (no LLM needed)."""
    
    objects = {}
    scenes = screenplay_data.get("scenes", [])
    
    # Object detection patterns
    object_patterns = [
        r'\b(basket|bag|box|container)\b',
        r'\b(door|window|table|chair|bed)\b',
        r'\b(cloak|dress|hat|shoes|clothing)\b',
        r'\b(bread|cake|wine|food|treats)\b',
        r'\b(knife|sword|tool|weapon)\b',
        r'\b(book|letter|paper|document)\b',
        r'\b(candle|lamp|light|torch)\b'
    ]
    
    object_categories = {
        "props": ["basket", "bag", "box", "container", "knife", "sword", "tool", "weapon", "book", "letter", "candle", "lamp"],
        "furniture": ["door", "window", "table", "chair", "bed"],
        "clothing": ["cloak", "dress", "hat", "shoes"],
        "food": ["bread", "cake", "wine", "food", "treats"]
    }
    
    for scene in scenes:
        scene_num = scene["scene_number"]
        action_blocks = scene.get("action_blocks", [])
        
        for block in action_blocks:
            # Find objects using patterns
            for pattern in object_patterns:
                matches = re.findall(pattern, block, re.IGNORECASE)
                
                for match in matches:
                    object_id = self._create_object_id(match)
                    
                    if object_id not in objects:
                        objects[object_id] = {
                            "object_id": object_id,
                            "object_name": match.title(),
                            "object_category": self._categorize_object(match, object_categories),
                            "visual_description": self._extract_visual_from_context(match, block),
                            "extraction_method": "text_analysis",
                            "confidence": 0.8,
                            "text_references": [],
                            "scene_appearances": [],
                            "object_properties": self._extract_properties(match, block),
                            "related_characters": [],
                            "story_importance": "medium"
                        }
                    
                    # Add reference and scene
                    objects[object_id]["text_references"].append(block)
                    if scene_num not in objects[object_id]["scene_appearances"]:
                        objects[object_id]["scene_appearances"].append(scene_num)
    
    return list(objects.values())

def _create_object_id(self, name: str) -> str:
    """Create snake_case identifier from object name."""
    return name.lower().replace(" ", "_").replace("'", "").replace("-", "_")

def _categorize_object(self, object_name: str, categories: Dict) -> str:
    """Categorize object based on predefined categories."""
    for category, items in categories.items():
        if object_name.lower() in items:
            return category
    return "other"

def _extract_visual_from_context(self, object_name: str, context: str) -> str:
    """Extract visual description from surrounding context."""
    # Look for adjectives and descriptive words near the object
    words = context.split()
    object_index = -1
    
    for i, word in enumerate(words):
        if object_name.lower() in word.lower():
            object_index = i
            break
    
    if object_index == -1:
        return ""
    
    # Extract surrounding descriptive words
    descriptors = []
    start = max(0, object_index - 3)
    end = min(len(words), object_index + 4)
    
    descriptive_words = ["red", "blue", "wooden", "metal", "large", "small", "old", "new", "beautiful", "rustic", "ornate"]
    
    for word in words[start:end]:
        clean_word = word.lower().strip(".,!?")
        if clean_word in descriptive_words:
            descriptors.append(clean_word)
    
    return " ".join(descriptors) if descriptors else ""
```

### 2. LLM Enhancement (Only for Ambiguous Cases)
```python
def enhance_ambiguous_objects(self, objects: List[Dict]) -> List[Dict]:
    """Use LLM only for objects with insufficient visual description."""
    
    ambiguous_objects = []
    enhanced_objects = []
    
    for obj in objects:
        if len(obj["visual_description"]) < 15:  # Minimal description threshold
            ambiguous_objects.append(obj)
        else:
            enhanced_objects.append(obj)
    
    if not ambiguous_objects:
        return objects  # No LLM needed
    
    # Use LLM only for ambiguous cases
    for obj in ambiguous_objects:
        enhanced = self._enhance_object_with_llm(obj)
        enhanced_objects.append(enhanced)
    
    return enhanced_objects

def _enhance_object_with_llm(self, object_data: Dict) -> Dict:
    """Use LLM to enhance object with minimal visual description."""
    
    prompt = f"""You are a visual description specialist for storyboard generation.

Object: {object_data['object_name']}
Category: {object_data['object_category']}
Current description: {object_data['visual_description']}
Text references: {object_data['text_references']}

Provide a concise visual description for storyboard generation. Focus on:
- Material and texture
- Color and finish
- Size and shape
- Key visual characteristics

Respond with JSON:
{{
  "enhanced_visual_description": "detailed visual description here",
  "enhancement_notes": "explanation of additions made"
}}"""

    try:
        response = self.llm.invoke(prompt)
        result = self._parse_llm_response(response)
        
        object_data["visual_description"] = result.get("enhanced_visual_description", object_data["visual_description"])
        object_data["extraction_method"] = "llm_enhanced"
        object_data["enhancement_notes"] = result.get("enhancement_notes", "")
        
        return object_data
        
    except Exception as e:
        # Fallback: keep original description
        object_data["enhancement_notes"] = f"LLM enhancement failed: {str(e)}"
        return object_data
```

### 3. Object-Character Relationship Detection
```python
def detect_object_relationships(self, objects: List[Dict], characters: List[Dict]) -> List[Dict]:
    """Detect relationships between objects and characters."""
    
    relationship_verbs = ["carries", "holds", "wears", "uses", "owns", "grabs", "picks up", "puts down"]
    
    for obj in objects:
        obj["related_characters"] = []
        
        for ref in obj["text_references"]:
            for verb in relationship_verbs:
                if verb in ref.lower():
                    # Find character in the same reference
                    for char in characters:
                        if char["character_name"].lower() in ref.lower():
                            if char["character_id"] not in obj["related_characters"]:
                                obj["related_characters"].append(char["character_id"])
    
    return objects

def assess_story_importance(self, objects: List[Dict]) -> List[Dict]:
    """Assess the importance of objects to the story."""
    
    for obj in objects:
        appearances = len(obj["scene_appearances"])
        references = len(obj["text_references"])
        
        if appearances >= 3 or references >= 3:
            obj["story_importance"] = "high"
        elif appearances >= 2 or references >= 2:
            obj["story_importance"] = "medium"
        else:
            obj["story_importance"] = "low"
    
    return objects
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
import re
from typing import Dict, List

class ObjectExtractor(Component):
    display_name = "003 Object Extractor"
    description = "Extract objects and props with visual descriptions for storyboard generation"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Extracted Objects", name="extracted_objects", method="extract_objects")
    ]
    
    def extract_objects(self) -> Data:
        """Main extraction method with minimal LLM usage."""
        try:
            screenplay_data = self.parsed_screenplay.data
            
            if not screenplay_data or "scenes" not in screenplay_data:
                return Data(data=self._create_error_response("Invalid screenplay data"))
            
            # Step 1: Extract objects using text analysis (Python only)
            objects = self.extract_objects_from_text(screenplay_data)
            
            # Step 2: Assess story importance (Python only)
            objects = self.assess_story_importance(objects)
            
            # Step 3: Enhance only ambiguous cases with LLM (minimal usage)
            objects = self.enhance_ambiguous_objects(objects)
            
            # Step 4: Create summary
            summary = self._create_extraction_summary(objects)
            
            result = {
                "extracted_objects": objects,
                "extraction_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Object extraction failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm` (used minimally)

**Output:** `extracted_objects` → Description Enhancer

### Benefits of This Approach
- ✅ **Focused extraction** - Only handles objects and props
- ✅ **Minimal LLM usage** - Python does most work
- ✅ **Industry standard** - snake_case identifiers
- ✅ **No limitations** - Processes complete movies
- ✅ **Better organization** - Separated from characters/environments
- ✅ **Story importance assessment** - Prioritizes key objects

## Next Steps
1. Implement in parallel with Character Extractor
2. Test object extraction with your narrative
3. Verify minimal LLM usage
4. Connect to Environment Extractor (parallel processing)
5. Continue with separated extraction architecture
