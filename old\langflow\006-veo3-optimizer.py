from langflow.custom import Component
from langflow.io import DataInput, MessageTextInput, DropdownInput, Output
from langflow.schema import Data

class VEO3Optimizer(Component):
    display_name = "VEO3 Prompt Optimizer"
    description = "Optimizes prompts specifically for VEO3 video generation"
    
    inputs = [
        DataInput(name="consistent_prompts", display_name="Consistent Prompts"),
        MessageTextInput(name="max_characters", display_name="Max Characters", value="500"),
        DropdownInput(name="detail_level", display_name="Detail Level", 
                     options=["minimal", "standard", "detailed"], value="standard"),
        MessageTextInput(name="aspect_ratio", display_name="Aspect Ratio", value="16:9"),
        MessageTextInput(name="duration", display_name="Duration", value="8 seconds")
    ]
    
    outputs = [
        Output(display_name="VEO3 Prompts", name="veo3_prompts", method="optimize_for_veo3")
    ]
    
    def optimize_for_veo3(self) -> Data:
        prompts_data = self.consistent_prompts.data
        max_chars = int(self.max_characters or "500")
        detail_level = self.detail_level or "standard"
        aspect_ratio = self.aspect_ratio or "16:9"
        duration = self.duration or "8 seconds"
        
        system_prompt = f"""You are an expert in optimizing prompts for VEO3 (Google's video generation model). Your expertise includes:

1. VEO3-specific prompt engineering best practices
2. Character limit optimization while maintaining quality
3. Technical parameter integration (aspect ratio, duration)
4. Visual clarity and specificity for video generation
5. Maintaining narrative consistency across video segments

VEO3 Optimization Guidelines:
- Keep prompts under {max_chars} characters for optimal performance
- Use clear, specific visual language
- Include camera movements and angles when relevant
- Specify lighting and mood for better results
- Maintain subject consistency across prompts
- Use cinematic terminology that VEO3 understands well

Detail Level: {detail_level}
- Minimal: Essential visual elements only
- Standard: Balanced detail with key visual and technical elements
- Detailed: Comprehensive description with full technical specifications

Technical Requirements:
- Aspect Ratio: {aspect_ratio}
- Duration: {duration}
- Character Limit: {max_chars}"""

        # Optimize each prompt for VEO3
        optimized_data = self._optimize_prompts(prompts_data, max_chars, detail_level, aspect_ratio, duration)
        
        return Data(data=optimized_data)
    
    def _optimize_prompts(self, prompts_data, max_chars, detail_level, aspect_ratio, duration):
        """
        This method would typically call an LLM API for VEO3 optimization.
        For implementation, you would integrate with your preferred LLM service.
        """
        consistent_prompts = prompts_data.get("consistent_prompts", [])
        optimized_prompts = []
        
        for prompt in consistent_prompts:
            # Optimize each prompt for VEO3
            optimized = self._create_veo3_prompt(prompt, max_chars, detail_level, aspect_ratio, duration)
            optimized_prompts.append(optimized)
        
        return {
            "veo3_prompts": optimized_prompts,
            "total_prompts": len(optimized_prompts),
            "optimization_settings": {
                "max_characters": max_chars,
                "detail_level": detail_level,
                "aspect_ratio": aspect_ratio,
                "duration": duration
            }
        }
    
    def _create_veo3_prompt(self, prompt, max_chars, detail_level, aspect_ratio, duration):
        """Create an optimized VEO3 prompt"""
        base_prompt = prompt["image_prompt"]
        shot_number = prompt["shot_number"]
        
        # Add VEO3-specific formatting based on detail level
        if detail_level == "minimal":
            veo3_prompt = base_prompt
        elif detail_level == "standard":
            veo3_prompt = f"Cinematic video shot: {base_prompt}"
        else:  # detailed
            veo3_prompt = f"Professional cinematic video, {duration}, {aspect_ratio}: {base_prompt}"
        
        # Optimize for character limit
        if len(veo3_prompt) > max_chars:
            # Intelligent truncation while preserving key elements
            veo3_prompt = self._intelligent_truncate(veo3_prompt, max_chars)
        
        # Add VEO3-specific technical parameters
        technical_params = {
            "aspect_ratio": aspect_ratio,
            "duration": duration,
            "quality": "high",
            "motion": "medium"
        }
        
        return {
            "prompt_number": shot_number,
            "veo3_prompt": veo3_prompt,
            "character_count": len(veo3_prompt),
            "technical_parameters": technical_params,
            "optimization_notes": f"Optimized for VEO3 with {detail_level} detail level",
            "subject_consistency": prompt.get("subject_updates", [])
        }
    
    def _intelligent_truncate(self, prompt, max_chars):
        """Intelligently truncate prompt while preserving important elements"""
        if len(prompt) <= max_chars:
            return prompt
        
        # Priority preservation: subjects > actions > descriptions > modifiers
        # This is a simplified version - would use AI for intelligent truncation
        
        # Remove less critical words first
        words = prompt.split()
        truncated = prompt
        
        # Remove adjectives and adverbs first, then less critical elements
        remove_words = ["very", "quite", "extremely", "really", "somewhat", "rather"]
        
        for word in remove_words:
            truncated = truncated.replace(f" {word} ", " ")
            if len(truncated) <= max_chars:
                break
        
        # If still too long, truncate from the end
        if len(truncated) > max_chars:
            truncated = truncated[:max_chars-3] + "..."
        
        return truncated