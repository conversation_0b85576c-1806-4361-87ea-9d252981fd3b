flowchart LR
    A[File<br/>Little-Red-Riding-H...] --> PT1_narrative[narrative]
    PT1_subjects[subjects] --> PT1[Prompt Template 1]
    PT1_ambiguities[ambiguities] --> PT1
    PT1_narrative --> PT1
    
    TI1[Text Input<br/>System Message] --> LM1_System[System Message]
    PT1 --> PT1_out[Template]
    PT1_out --> LM1_Input[Input]
    
    LM1_Input --> LM1[Language Model 1]
    LM1_System --> LM1
    LM1 --> LM1_out[Model Response]
    
    LM1_out --> PT2_enhanced[enhanced_subjects]
    PT2_narrative[narrative] --> PT2[Prompt Template 2]
    PT2_scene[scene_input] --> PT2
    PT2_enhanced --> PT2
    
    TI2[Text Input<br/>System Message] --> LM2_System[System Message]
    PT2 --> PT2_out[Template]
    PT2_out --> LM2_Input[Input]
    
    LM2_Input --> LM2[Language Model 2]
    LM2_System --> LM2
    LM2 --> LM2_out[Model Response]
    
    LM2_out --> PT3_enhanced[enhanced_subjects]
    PT3_included[included_subjects] --> PT3[Prompt Template 3]
    PT3_scene_desc[scene_description] --> PT3
    PT3_elements[scene_elements] --> PT3
    PT3_enhanced --> PT3
    
    TI3[Text Input<br/>System Message] --> LM3_System[System Message]
    PT3 --> PT3_out[Template]
    PT3_out --> LM3_Input[Input]
    
    LM3_Input --> LM3[Language Model 3]
    LM3_System --> LM3
    LM3 --> LM3_out[Model Response]
    
    LM3_out --> PT4_enhanced[enhanced_description]
    PT4_narrative[narrative_context] --> PT4[Prompt Template 4]
    PT4_consistency[subject_consistency] --> PT4
    PT4_subjects[subject_description] --> PT4
    PT4_enhanced --> PT4
    
    TI4[Text Input<br/>System Message] --> LM4_System[System Message]
    PT4 --> PT4_out[Template]
    PT4_out --> LM4_Input[Input]
    
    LM4_Input --> LM4[Language Model 4]
    LM4_System --> LM4
    LM4 --> LM4_out[Model Response]
    
    LM4_out --> PT5_input1[input1]
    PT5_input2[input2] --> PT5[Prompt Template 5]
    PT5_input3[input3] --> PT5
    PT5_input4[input4] --> PT5
    PT5_input1 --> PT5
    
    TI5[Text Input<br/>System Message] --> LM5_System[System Message]
    PT5 --> PT5_out[Template]
    PT5_out --> LM5_Input[Input]
    
    LM5_Input --> LM5[Language Model 5]
    LM5_System --> LM5
    LM5 --> LM5_out[Model Response]
    
    %% Styling
    classDef file fill:#e8f5e8
    classDef textinput fill:#fff3e0
    classDef llm fill:#e1f5fe
    classDef prompt fill:#f3e5f5
    classDef io fill:#ffecb3
    
    class A file
    class TI1,TI2,TI3,TI4,TI5 textinput
    class LM1,LM2,LM3,LM4,LM5 llm
    class PT1,PT2,PT3,PT4,PT5 prompt