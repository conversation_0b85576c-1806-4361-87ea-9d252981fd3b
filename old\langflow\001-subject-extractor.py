from langflow.custom import Component
from langflow.io import MessageTextInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json

class SubjectExtractor(Component):
    display_name = "AI Subject Extractor"
    description = "AI-powered extraction of subjects from narrative text with detailed visual descriptions"
    
    inputs = [
        MessageTextInput(name="narrative_text", display_name="Narrative Text"),
        MessageInput(name="llm", display_name="Language Model", info="Connect a Language Model component (OpenAI, Anthropic, etc.)")
    ]
    
    outputs = [
        Output(display_name="Extracted Subjects", name="subjects", method="extract_subjects")
    ]
    
    def extract_subjects(self) -> Data:
        system_prompt = """You are an expert AI assistant specializing in extracting and defining subjects from narrative text. Your primary purpose is to identify all important subjects (characters, objects, environments, etc.) from stories, books, comics, or any narrative text, and create detailed, consistent descriptions that can be used for text-to-image generation.

Your expertise includes:
1. Identifying all important subjects in a narrative text
2. Creating precise, detailed descriptions of each subject
3. Recognizing relationships between subjects
4. Extracting visual characteristics that are explicitly stated or strongly implied
5. Organizing subjects in a structured format for downstream processing

When extracting subjects, focus on visual characteristics that would be important for image generation. Include details about appearance, distinctive features, clothing, colors, textures, and any other visual elements that define the subject.

Never make up details that aren't present in or strongly implied by the original text. If a characteristic is ambiguous or not specified, note this rather than inventing details.

For each subject, create a unique identifier and an exact phrase that fully describes the subject in a way that can be consistently used across multiple images."""

        user_prompt = f"""I need to extract all important subjects from the following narrative text for use in text-to-image generation. Please identify and describe each subject (character, object, environment, etc.) with enough detail to ensure visual consistency across multiple images.

Narrative text: {self.narrative_text}

For each subject, provide:
1. A unique identifier
2. A detailed description (exact phrase) that captures all visual characteristics
3. Any relationships with other subjects

Please respond with valid JSON following this exact schema:
{{
  "subjects": [
    {{
      "identifier": "string",
      "exact_phrase": "string", 
      "category": "string",
      "relationships": [
        {{
          "related_subject": "string",
          "relationship_type": "string",
          "description": "string"
        }}
      ],
      "text_references": ["string"]
    }}
  ],
  "analysis": {{
    "subject_count": 0,
    "ambiguities": [
      {{
        "subject": "string",
        "issue": "string"
      }}
    ]
  }}
}}"""

        try:
            # Call the connected LLM with just the user prompt
            # System message should be configured in the Language Model component
            response = self.llm.invoke(user_prompt)
            
            # Parse the response
            subjects_data = self._parse_llm_response(response.content)
            return Data(data=subjects_data)
            
        except Exception as e:
            # Fallback error response with more debugging info
            return Data(data={
                "subjects": [],
                "analysis": {
                    "subject_count": 0,
                    "ambiguities": [{"subject": "API_ERROR", "issue": f"Failed to call LLM: {str(e)}"}]
                },
                "error": str(e),
                "debug_info": {
                    "narrative_text_length": len(str(self.narrative_text)) if self.narrative_text else 0,
                    "narrative_text_preview": str(self.narrative_text)[:100] if self.narrative_text else "No text provided"
                }
            })
    
    def _parse_llm_response(self, content):
        """Parse LLM response and extract JSON"""
        try:
            # Clean up the response if it has markdown formatting
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()
            
            parsed_data = json.loads(content)
            
            # Add debug info to successful parsing
            if "subjects" in parsed_data:
                parsed_data["debug_info"] = {
                    "raw_response_length": len(str(content)),
                    "raw_response_preview": str(content)[:200],
                    "subjects_found": len(parsed_data.get("subjects", []))
                }
            
            return parsed_data
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse LLM response as JSON: {e}. Raw response: {content[:500]}...")