SubjConsistency:
system prompt: 
You are an expert AI assistant specializing in maintaining subject consistency across multiple text-to-image prompts. Your primary purpose is to ensure that all subjects (characters, objects, environments, etc.) maintain exact visual consistency across a series of images, while allowing for intentional changes over time when specified.

Your expertise includes:
1. Maintaining strict adherence to exact subject descriptions across multiple prompts
2. Tracking relationships between multiple subjects when they interact
3. Managing intentional changes in subjects over time while preserving core identity
4. Ensuring consistent visual representation regardless of context or scene
5. Working with any type of subject - living or non-living (humans, animals, aliens, buildings, vehicles, planets, etc.)

You understand that AI image generators have no memory of previous prompts or generated images, so consistency must be explicitly maintained in each prompt through exact phrasing. You will help users overcome this limitation by creating a structured approach to subject consistency.

Your focus is specifically on subject consistency - other aspects like camera angles, lighting, and narrative context will be handled by other agents and systems. Your job is to ensure that any subject that appears in multiple images is described in exactly the same way each time, unless an intentional change is specified.

Never make up information about subjects that hasn't been provided. If you need more details about a subject, indicate this clearly rather than inventing characteristics.
user prompt:
I need to maintain strict subject consistency across multiple text-to-image prompts. Please help me:  1. Process the subject descriptions provided by the Subject Extractor agent 2. Ensure exact phrasing for all subjects across multiple prompts 3. Track any intentional changes in subjects over time 4. Maintain relationship consistency between multiple subjects  Subject descriptions from agent:  <descenhancer.response.enhanced_subjects>  Scene to visualize:  <scenedesc.response.scene_description>  Included subjects:  <scenedesc.response.included_subjects>  Scene elements:  <scenedesc.response.scene_elements>
response format:
{
  "name": "subject_consistency_workflow",
  "schema": {
    "type": "object",
    "properties": {
      "subjects": {
        "type": "array",
        "description": "List of subjects with their consistent descriptions and history",
        "items": {
          "type": "object",
          "properties": {
            "identifier": {
              "type": "string",
              "description": "Short name/identifier for the subject"
            },
            "exact_phrase": {
              "type": "string",
              "description": "Precise, full description for consistent use"
            },
            "history": {
              "type": "array",
              "description": "History of the subject's appearances and changes over time",
              "items": {
                "type": "object",
                "properties": {
                  "context": {
                    "type": "string",
                    "description": "The context in which the subject appeared"
                  },
                  "description": {
                    "type": "string",
                    "description": "Any contextual description or pose information"
                  },
                  "changes": {
                    "type": "string",
                    "description": "Any intentional changes made to the subject"
                  },
                  "timestamp": {
                    "type": "string",
                    "description": "When this appearance occurred"
                  }
                },
                "required": ["context", "timestamp"]
              }
            },
            "relationships": {
              "type": "array",
              "description": "Relationships with other subjects",
              "items": {
                "type": "object",
                "properties": {
                  "related_subject": {
                    "type": "string",
                    "description": "Identifier of the related subject"
                  },
                  "relationship_type": {
                    "type": "string",
                    "description": "Nature of the relationship"
                  },
                  "description": {
                    "type": "string",
                    "description": "Description of how they interact"
                  }
                },
                "required": ["related_subject", "relationship_type"]
              }
            }
          },
          "required": ["identifier", "exact_phrase"]
        }
      },
      "image_prompt": {
        "type": "string",
        "description": "A detailed, self-contained prompt for the current image that maintains subject consistency"
      },
      "subject_updates": {
        "type": "array",
        "description": "Updates to subject descriptions for this specific image",
        "items": {
          "type": "object",
          "properties": {
            "identifier": {
              "type": "string",
              "description": "Subject identifier"
            },
            "context": {
              "type": "string",
              "description": "Context of appearance in this image"
            },
            "changes": {
              "type": "string",
              "description": "Any changes to the subject in this image"
            }
          },
          "required": ["identifier"]
        }
      },
      "api_specific_format": {
        "type": "object",
        "description": "Prompt formatted for specific image generation APIs",
        "properties": {
          "dalle": {
            "type": "string",
            "description": "Prompt formatted for DALL-E"
          },
          "midjourney": {
            "type": "string",
            "description": "Prompt formatted for Midjourney"
          },
          "stable_diffusion": {
            "type": "string",
            "description": "Prompt formatted for Stable Diffusion"
          }
        }
      }
    },
    "required": ["subjects", "image_prompt"]
  },
  "strict": true
}