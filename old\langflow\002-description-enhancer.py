from langflow.custom import Component
from langflow.io import DataInput, MessageTextInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json

class DescriptionEnhancer(Component):
    display_name = "AI Description Enhancer"
    description = "AI-powered enhancement of incomplete visual descriptions for text-to-image generation"
    
    inputs = [
        DataInput(name="extracted_subjects", display_name="Extracted Subjects"),
        MessageTextInput(name="original_narrative", display_name="Original Narrative"),
        MessageInput(name="llm", display_name="Language Model", info="Connect a Language Model component (OpenAI, Anthropic, etc.)")
    ]
    
    outputs = [
        Output(display_name="Enhanced Subjects", name="enhanced_subjects", method="enhance_descriptions")
    ]
    
    def enhance_descriptions(self) -> Data:
        subjects_data = self.extracted_subjects.data
        narrative = self.original_narrative
        
        system_prompt = """You are an expert AI assistant specializing in enhancing incomplete visual descriptions for text-to-image generation. Your primary purpose is to take subjects extracted from narrative text that have ambiguous or missing visual details and provide consistent, appropriate visual descriptions for them.

Your expertise includes:
1. Identifying subjects with incomplete or missing visual descriptions
2. Creating appropriate, consistent visual details for these subjects
3. Maintaining the tone and style of the original narrative
4. Ensuring descriptions are detailed enough for high-quality image generation
5. Providing consistent descriptions for similar character types across different stories

When enhancing descriptions:
- Clearly distinguish between details explicitly stated in the original text and your enhancements
- Maintain consistency with any existing visual details provided
- Consider the time period, setting, and genre of the story
- Provide age-appropriate descriptions for the apparent target audience
- Ensure descriptions are visually coherent and would work well for image generation
- For traditional fairy tales or well-known stories, use culturally familiar visual representations unless otherwise specified

Always mark your enhancements with [ENHANCED] at the beginning of any added details, so it's clear which parts were not in the original text."""

        user_prompt = f"""I need to enhance incomplete visual descriptions for subjects extracted from a narrative text. Please review the following subjects and provide complete visual descriptions for any that have ambiguous or missing details.

Extracted subjects: {subjects_data.get('subjects', [])}
Ambiguities identified: {subjects_data.get('analysis', {}).get('ambiguities', [])}
Original narrative text (for context): {narrative}

For each subject with incomplete visual details:
1. Maintain all existing visual details from the original text
2. Add [ENHANCED] details where needed for complete visual description
3. Ensure consistency with the narrative's tone and setting
4. Provide enough detail for high-quality image generation

Please respond with valid JSON following this exact schema:
{{
  "enhanced_subjects": [
    {{
      "identifier": "string",
      "original_phrase": "string",
      "enhanced_phrase": "string",
      "category": "string",
      "enhancement_notes": "string",
      "relationships": [
        {{
          "related_subject": "string",
          "relationship_type": "string",
          "description": "string"
        }}
      ],
      "text_references": ["string"],
      "ambiguities_resolved": [
        {{
          "original_issue": "string",
          "resolution": "string"
        }}
      ]
    }}
  ],
  "enhancement_summary": {{
    "subjects_enhanced": 0,
    "subjects_unchanged": 0,
    "enhancement_approach": "string",
    "total_ambiguities_resolved": 0
  }}
}}"""

        try:
            # Call the connected LLM with just the user prompt
            # System message should be configured in the Language Model component
            response = self.llm.invoke(user_prompt)
            
            # Parse the response
            enhanced_data = self._parse_llm_response(response.content)
            return Data(data=enhanced_data)
            
        except Exception as e:
            # Fallback error response
            return Data(data={
                "enhanced_subjects": [],
                "enhancement_summary": {
                    "subjects_enhanced": 0,
                    "subjects_unchanged": 0,
                    "enhancement_approach": "ERROR",
                    "total_ambiguities_resolved": 0
                },
                "error": str(e)
            })
    
    def _parse_llm_response(self, content):
        """Parse LLM response and extract JSON"""
        try:
            # Clean up the response if it has markdown formatting
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()
            
            return json.loads(content)
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse LLM response as JSON: {e}. Response: {content}")