# Project Documentation

## Project Overview

This is a **Text-to-Video Workflow Generator** project designed to create professional, industry-standard prompts for AI video generators. The project focuses on generating consistent video prompts from screenplays, scripts, and stories, with particular emphasis on character consistency across multiple video segments.

### Primary Goals
- Generate multiple self-isolated prompts for AI video generators (currently optimized for VEO3 by Google)
- Maintain character and visual consistency across 400+ individual prompts
- Support industry-standard screenplay and script formats
- Create agnostic workflows that work with any AI video generator
- Follow golden standards for storytelling and film production

## Technology Stack

- **Primary Framework**: Langflow (Python-based workflow automation)
- **Language**: Python
- **Target Platform**: AI Video Generators (VEO3, with future expansion to Runway, Pika, Luma, etc.)
- **Output Formats**: JSON, plain text prompts
- **Virtual Environment**: uv
- **Future Integration**: Docker containers, web application APIs

## Project Structure

### `/langflow/` - Langflow Components
Contains Python components for the Langflow workflow system:

- `000-fixed-universal-screenplay-parser.py` - Parses screenplays and extracts characters, scenes, and action blocks
- `001-advanced-shot-list-generator.py` - Generates detailed shot lists from parsed screenplay data
- `002-professional-veo3-prompt-generator.py` - Creates VEO3-optimized prompts with length control and detail levels

### `/router2imageprompts/` - Router Workflow Components
Contains markdown files defining a multi-agent workflow for text-to-image generation:

- `00_router1.md` - Initial story input (Little Red Riding Hood example)
- `01_SubjExtractor.md` - Subject extraction agent with JSON schema
- `02_DescEnhancer.md` - Description enhancement agent for incomplete visual details
- `03_SceneDesc.md` - Scene description generator for image prompts or in our case screenboards where every single screenboard represents one shot or key moment in a scene.
- `04_SubjConsistency.md` - Subject consistency maintenance across multiple prompts
- `05.md` - Final formatting and output components

### `/doc/` - Documentation
- `past-conversation.md` - Detailed project requirements and development discussions

## Key Features

### Screenplay Processing
- Parses industry-standard screenplay formats (TV show, feature film)
- Extracts real speaking characters (not scene directions)
- Breaks scripts into 50-100+ individual action blocks/shots
- Supports multiple story structure systems (three-act, Hero's Journey, etc.)

### Prompt Generation
- Character length control (default 500 chars for VEO3)
- Detail level options: minimal, standard, detailed
- Aspect ratio specification (16:9 default)
- Duration control for video segments
- Self-isolated prompts (no memory dependency between prompts)

### Character Consistency
- Maintains exact visual descriptions across all prompts
- Tracks character relationships and interactions
- Handles intentional character changes over time
- Supports any subject type (humans, animals, objects, environments)

## Development Best Practices

### Code Organization
- Use descriptive component names with version numbers (000-, 001-, etc.)
- Separate concerns: parsing, generation, formatting, consistency
- Implement modular, interchangeable components
- Follow Langflow component structure with proper inputs/outputs

### Prompt Engineering
- Keep prompts under character limits for target AI models
- Use consistent terminology across all components
- Mark enhanced details with [ENHANCED] tags
- Maintain narrative consistency with original source material

### Data Handling
- Use structured JSON schemas for agent communication
- Preserve original text references for traceability
- Track ambiguities and missing details explicitly
- Implement proper error handling for malformed inputs

### Workflow Design
- Design for 100% modularity and component reusability
- Support full customization while providing guided experiences
- Plan for multi-format export (JSON, text, industry standards)
- Consider future expansion to multiple AI video generators

## File Naming Conventions

### Langflow Components
- Use numbered prefixes: `000-`, `001-`, `002-`
- Include descriptive names: `fixed-universal-screenplay-parser`
- Use kebab-case for filenames
- Include `.py` extension for Python components

### Router Components
- Use numbered prefixes: `000-`, `001-`, `002-`
- Use kebab-case for component names: `subject-extractor`, `desc-enhancer`
- Include `.md` extension for markdown specifications

## Testing and Validation

### Current Testing Approach
- Use AI regeneration for iterative improvements
- Focus on VEO3 optimization initially
- Test with classic stories: [text](examples/pdf/Hudsucker_Proxy.pdf)
- test with screenplay: [text](examples/pdf/Hudsucker_Proxy.pdf)
- Validate character count limits and prompt effectiveness

### Quality Metrics
- Number of shots generated (target: 50-100+ from full screenplay)
- Character consistency across prompts
- Prompt length compliance with AI model limits
- Narrative coherence and professional formatting

## Future Expansion Plans

### Content Types
- Narrative films, commercials, educational content
- TV shows, documentaries, social media content
- Animation, VR/AR, interactive content, gaming

### Technical Enhancements
- Multi-language support
- Enterprise integrations
- Production management tool connections

### AI Model Support
- VEO 3, Runway, Pika, Luma, Hailuo, KlingAI,etc
- Adapter pattern for model-specific optimizations
- Configuration-based approach for new generators

## Common Issues and Solutions

### Character Detection Problems
- **Issue**: Picking up scene numbers instead of character names
- **Solution**: Use regex patterns to filter out non-character elements
- **Code**: Check line length, uppercase format, and exclude technical terms

### Prompt Length Issues
- **Issue**: Prompts too long for AI video generators
- **Solution**: Implement character limits with trimming
- **Best Practice**: Default to 500 characters for VEO3, make configurable

### Consistency Problems
- **Issue**: Characters appear differently across prompts
- **Solution**: Use exact phrase matching and subject tracking
- **Implementation**: Maintain subject history and relationship tracking

## Getting Started

1. **Set up Langflow environment** with Python virtual environment
2. **Import components** from `/langflow/` directory into Langflow
3. **Configure workflow** with screenplay input and desired output format
4. **Test with sample screenplay** to validate prompt generation
5. **Adjust parameters** (character limits, detail levels) as needed
6. **Export results** in JSON or text format for AI video generator

## Dependencies and Requirements

- Python 3.x with Langflow framework
- Regular expression support for text parsing
- JSON handling capabilities
- Future: Docker for containerization
- Future: Elasticsearch for taxonomy/ontology search (avoid now)

---

*This documentation reflects the current development state and will be updated as the project evolves.*