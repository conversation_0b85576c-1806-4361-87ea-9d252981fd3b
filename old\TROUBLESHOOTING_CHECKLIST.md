# Subject Extractor Troubleshooting Checklist

## 🔍 **Step-by-Step Debugging**

### **1. Check Your Connections**
- [ ] Text Input component has story text
- [ ] Text Input → Language Model "Input" 
- [ ] Text Input → Subject Extractor "Narrative Text"
- [ ] Language Model → Subject Extractor "Language Model"

### **2. Verify Language Model Settings**
- [ ] Provider selected (OpenAI/Anthropic/Google)
- [ ] Model name chosen (e.g., gpt-4o)
- [ ] API key entered correctly
- [ ] System message copied exactly:
```
You are an expert AI assistant specializing in extracting and enhancing visual descriptions from narrative text for text-to-image generation. You excel at identifying subjects (characters, objects, environments) and creating detailed, consistent descriptions. Always respond with valid JSON when requested.
```

### **3. Test Input Text**
Use this exact text in your Text Input:
```
<PERSON> <PERSON> was a young girl who wore a red cloak. She met a big gray wolf in the forest. Her grandmother lived in a cottage.
```

### **4. Check Debug Output**
Look for these fields in the Subject Extractor output:
- `debug_info.narrative_text_length` - Should be > 0
- `debug_info.narrative_text_preview` - Should show your story
- `debug_info.raw_response_preview` - Shows AI response
- `error` - Any error messages

### **5. Common Issues**

**Issue: Empty subjects array**
- Check if narrative_text_length > 0
- Verify LLM connection is working
- Check raw_response_preview for AI output

**Issue: JSON parsing error**
- AI might not be returning valid JSON
- Check raw_response_preview
- Try different model (GPT-4o usually works best)

**Issue: API errors**
- Verify API key is correct
- Check rate limits
- Try different provider

### **6. Expected Good Output**
```json
{
  "subjects": [
    {
      "identifier": "little_red_riding_hood",
      "exact_phrase": "young girl wearing a red cloak",
      "category": "character"
    },
    {
      "identifier": "wolf", 
      "exact_phrase": "big gray wolf",
      "category": "character"
    }
  ],
  "analysis": {
    "subject_count": 2
  }
}
```

## 🚨 **If Still Not Working**

1. **Share your debug output** - Copy the entire JSON response
2. **Check Language Model separately** - Test if it responds to simple prompts
3. **Try different model** - Switch from Claude to GPT-4o or vice versa
4. **Verify API quotas** - Make sure you have API credits

## 💡 **Quick Fix Test**
1. Use the simple test text above
2. Run workflow
3. Copy the entire output JSON
4. Look for `debug_info` section
5. Share what you see in `raw_response_preview`