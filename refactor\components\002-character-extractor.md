# 002 Character Extractor Component

## Overview
**Type:** Mostly Python Component (Minimal LLM usage for ambiguous cases only)
**Purpose:** Extract characters with visual descriptions for storyboard generation
**Priority:** HIGH - Part of the separated extraction architecture

## LLM Usage Documentation
**When LLM is used:** Only for ambiguous character descriptions that cannot be determined from text analysis
**What LLM does:** Provides creative visual details for characters with minimal description
**Why LLM is needed:** Some characters may be mentioned without visual details, requiring creative interpretation
**Token usage:** Minimal - only called for unclear cases, not for every character

## Input Specification
```python
inputs = [
    DataInput(
        name="parsed_screenplay", 
        display_name="Parsed Screenplay",
        info="Structured screenplay data from parser"
    ),
    MessageInput(
        name="llm", 
        display_name="Language Model",
        info="Connect Gemini 2.5 (used only for ambiguous cases)"
    )
]
```

## Output Specification
```json
{
  "extracted_characters": [
    {
      "character_id": "little_red_riding_hood",
      "character_name": "Little Red Riding Hood",
      "visual_description": "young girl with red hooded cloak, carrying wicker basket, innocent expression",
      "extraction_method": "text_analysis",
      "confidence": 0.95,
      "text_references": [
        "Little Red <PERSON> Hood enters the cottage",
        "She carries a basket of goodies"
      ],
      "scene_appearances": [1, 2, 3],
      "character_traits": ["innocent", "curious", "brave"],
      "relationships": [
        {
          "related_character": "grandmother",
          "relationship_type": "family",
          "description": "granddaughter visiting grandmother"
        }
      ]
    }
  ],
  "extraction_summary": {
    "total_characters": 5,
    "text_extracted": 4,
    "llm_enhanced": 1,
    "ambiguous_cases": [
      {
        "character": "mysterious_figure",
        "issue": "No visual description provided in text",
        "resolution": "LLM provided creative interpretation"
      }
    ],
    "processing_notes": "Character extraction completed successfully"
  }
}
```

## Core Functionality

### 1. Text-Based Character Extraction (Primary Method)
```python
def extract_characters_from_text(self, screenplay_data: Dict) -> List[Dict]:
    """Extract characters using Python text analysis (no LLM needed)."""
    
    characters = {}
    scenes = screenplay_data.get("scenes", [])
    
    # Extract from parser's character list first
    parser_characters = screenplay_data.get("characters", [])
    
    for char in parser_characters:
        character_id = self._create_character_id(char["name"])
        
        characters[character_id] = {
            "character_id": character_id,
            "character_name": char["name"],
            "visual_description": self._extract_visual_from_mentions(char["mentions"]),
            "extraction_method": "text_analysis",
            "confidence": 0.9,
            "text_references": char["mentions"],
            "scene_appearances": char.get("scene_appearances", []),
            "character_traits": self._extract_traits_from_text(char["mentions"]),
            "relationships": []
        }
    
    # Enhance with scene context
    for scene in scenes:
        self._enhance_characters_from_scene(characters, scene)
    
    return list(characters.values())

def _create_character_id(self, name: str) -> str:
    """Create snake_case identifier from character name."""
    return name.lower().replace(" ", "_").replace("'", "").replace("-", "_")

def _extract_visual_from_mentions(self, mentions: List[str]) -> str:
    """Extract visual description from text mentions."""
    visual_keywords = [
        "red", "blue", "tall", "short", "young", "old", "wearing", "carrying",
        "hooded", "cloak", "dress", "hat", "beard", "hair", "eyes"
    ]
    
    visual_elements = []
    for mention in mentions:
        for keyword in visual_keywords:
            if keyword.lower() in mention.lower():
                # Extract surrounding context
                words = mention.split()
                if keyword.lower() in [w.lower() for w in words]:
                    idx = [w.lower() for w in words].index(keyword.lower())
                    context = " ".join(words[max(0, idx-2):idx+3])
                    visual_elements.append(context)
    
    return ", ".join(set(visual_elements)) if visual_elements else ""
```

### 2. LLM Enhancement (Only for Ambiguous Cases)
```python
def enhance_ambiguous_characters(self, characters: List[Dict]) -> List[Dict]:
    """Use LLM only for characters with insufficient visual description."""
    
    ambiguous_characters = []
    enhanced_characters = []
    
    for char in characters:
        if len(char["visual_description"]) < 20:  # Minimal description threshold
            ambiguous_characters.append(char)
        else:
            enhanced_characters.append(char)
    
    if not ambiguous_characters:
        return characters  # No LLM needed
    
    # Use LLM only for ambiguous cases
    for char in ambiguous_characters:
        enhanced = self._enhance_character_with_llm(char)
        enhanced_characters.append(enhanced)
    
    return enhanced_characters

def _enhance_character_with_llm(self, character: Dict) -> Dict:
    """Use LLM to enhance character with minimal visual description."""
    
    prompt = f"""You are a visual description specialist for storyboard generation.

Character: {character['character_name']}
Current description: {character['visual_description']}
Text references: {character['text_references']}

Provide a concise visual description for storyboard generation. Focus on:
- Physical appearance
- Clothing/costume
- Key visual characteristics
- Age and build

Respond with JSON:
{{
  "enhanced_visual_description": "detailed visual description here",
  "enhancement_notes": "explanation of additions made"
}}"""

    try:
        response = self.llm.invoke(prompt)
        result = self._parse_llm_response(response)
        
        character["visual_description"] = result.get("enhanced_visual_description", character["visual_description"])
        character["extraction_method"] = "llm_enhanced"
        character["enhancement_notes"] = result.get("enhancement_notes", "")
        
        return character
        
    except Exception as e:
        # Fallback: keep original description
        character["enhancement_notes"] = f"LLM enhancement failed: {str(e)}"
        return character
```

### 3. Relationship Detection
```python
def detect_character_relationships(self, characters: List[Dict], scenes: List[Dict]) -> List[Dict]:
    """Detect relationships between characters using text analysis."""
    
    relationship_keywords = {
        "family": ["mother", "father", "grandmother", "grandfather", "daughter", "son", "sister", "brother"],
        "friend": ["friend", "companion", "ally"],
        "enemy": ["enemy", "villain", "antagonist", "foe"],
        "romantic": ["lover", "husband", "wife", "boyfriend", "girlfriend"],
        "professional": ["boss", "employee", "colleague", "partner"]
    }
    
    for char in characters:
        char["relationships"] = []
        
        # Analyze text references for relationship indicators
        for ref in char["text_references"]:
            for rel_type, keywords in relationship_keywords.items():
                for keyword in keywords:
                    if keyword in ref.lower():
                        # Find related character
                        related_char = self._find_related_character(ref, characters, char)
                        if related_char:
                            relationship = {
                                "related_character": related_char["character_id"],
                                "relationship_type": rel_type,
                                "description": f"{char['character_name']} is {keyword} of {related_char['character_name']}"
                            }
                            char["relationships"].append(relationship)
    
    return characters
```

## Implementation Details

### Complete Component Code Structure
```python
from langflow.custom import Component
from langflow.io import DataInput, Output
from langflow.inputs import MessageInput
from langflow.schema import Data
import json
import re
from typing import Dict, List

class CharacterExtractor(Component):
    display_name = "002 Character Extractor"
    description = "Extract characters with visual descriptions for storyboard generation"
    
    inputs = [
        DataInput(name="parsed_screenplay", display_name="Parsed Screenplay"),
        MessageInput(name="llm", display_name="Language Model")
    ]
    
    outputs = [
        Output(display_name="Extracted Characters", name="extracted_characters", method="extract_characters")
    ]
    
    def extract_characters(self) -> Data:
        """Main extraction method with minimal LLM usage."""
        try:
            screenplay_data = self.parsed_screenplay.data
            
            if not screenplay_data or "scenes" not in screenplay_data:
                return Data(data=self._create_error_response("Invalid screenplay data"))
            
            # Step 1: Extract characters using text analysis (Python only)
            characters = self.extract_characters_from_text(screenplay_data)
            
            # Step 2: Detect relationships (Python only)
            characters = self.detect_character_relationships(characters, screenplay_data["scenes"])
            
            # Step 3: Enhance only ambiguous cases with LLM (minimal usage)
            characters = self.enhance_ambiguous_characters(characters)
            
            # Step 4: Create summary
            summary = self._create_extraction_summary(characters)
            
            result = {
                "extracted_characters": characters,
                "extraction_summary": summary
            }
            
            return Data(data=result)
            
        except Exception as e:
            return Data(data=self._create_error_response(f"Character extraction failed: {str(e)}"))
```

## Langflow Integration

### Connection Instructions
**Inputs:**
- Screenplay Parser → `parsed_screenplay`
- Language Model (Gemini) → `llm` (used minimally)

**Output:** `extracted_characters` → Description Enhancer

### Benefits of This Approach
- ✅ **Focused extraction** - Only handles characters
- ✅ **Minimal LLM usage** - Python does most work
- ✅ **Industry standard** - snake_case identifiers
- ✅ **No limitations** - Processes complete movies
- ✅ **Better organization** - Separated from objects/environments
- ✅ **Robust error handling** - Graceful LLM failures

## Next Steps
1. Implement after Screenplay Parser
2. Test character extraction with your narrative
3. Verify minimal LLM usage
4. Connect to Object Extractor (parallel processing)
5. Continue with separated extraction architecture
