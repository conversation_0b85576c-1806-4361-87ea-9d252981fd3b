# Practical Golden Standards for Text-to-Video Workflow

## Solo Dev Approach - 7 Core Components

Simple, AI-powered workflow that follows film industry standards without overengineering.

## Core Principle: AI Does the Heavy Lifting

- **No hardcoded lists** - AI decides everything based on context
- **Simple data flow** - Each component has one clear job
- **Fast implementation** - Copy/paste ready Langflow components

## 7-Component Workflow (Ready to Code)

### **000-screenplay-parser.py** (Enhanced)
- Parse screenplay + extract characters and scenes
- AI identifies character descriptions and scene moods

### **001-subject-extractor.py** (Convert from SubjExtractor.md)
- Extract all subjects (characters, objects, locations) with visual descriptions
- **Convert JSON schema to Langflow Component class structure**

### **002-description-enhancer.py** (Convert from DescEnhancer.md)
- Enhance incomplete visual descriptions for subjects
- **Convert JSON schema to Langflow Component class structure**

### **003-shot-generator.py**
- AI breaks screenplay into cinematic shots
- Determines camera angles, movements, framing based on story context

### **004-scene-descriptor.py** (Convert from SceneDesc.md)
- Create detailed scene descriptions for image generation
- **Convert JSON schema to Langflow Component class structure**

### **005-consistency-manager.py** (Convert from SubjConsistency.md)
- Maintain subject consistency across multiple prompts
- **Convert JSON schema to Langflow Component class structure**

### **006-veo3-optimizer.py** 
- AI generates final VEO3 prompts
- Optimizes for character limits and platform requirements

## Data Flow
```
Screenplay → Parse → Extract Subjects → Enhance Descriptions → Generate Shots → Scene Descriptions → Consistency Management → VEO3 Prompts
```

## Key AI Decisions Per Component

**003-shot-generator.py**: AI analyzes each scene and decides:
- Camera angle (close-up for emotion, wide for action)
- Camera movement (static for dialogue, dynamic for action)
- Shot duration (quick cuts for tension, long takes for drama)

**004-scene-descriptor.py**: AI adds:
- Lighting (soft for romance, harsh for thriller)
- Mood (warm colors for happy, cool for sad)
- Atmosphere (fog for mystery, bright for comedy)

**Character/Object Consistency**: Convert existing .md workflow to .py:
- SubjExtractor.md → 001-subject-extractor.py (same logic, Langflow format)
- DescEnhancer.md → 002-description-enhancer.py (same logic, Langflow format)
- SubjConsistency.md → 005-consistency-manager.py (same logic, Langflow format)

## Implementation Order
1. Start with 000-screenplay-parser.py (enhance existing)
2. Convert 001-subject-extractor.py (from SubjExtractor.md)
3. Convert 002-description-enhancer.py (from DescEnhancer.md)
4. Build 003-shot-generator.py (replace current shot generator)
5. Convert 004-scene-descriptor.py (from SceneDesc.md)
6. Convert 005-consistency-manager.py (from SubjConsistency.md)
7. Enhance 006-veo3-optimizer.py (improve current)

## Conversion Priority
**High Priority** (Convert .md JSON schemas to .py Langflow Components):
- SubjExtractor.md → 001-subject-extractor.py
- DescEnhancer.md → 002-description-enhancer.py
- SceneDesc.md → 004-scene-descriptor.py
- SubjConsistency.md → 005-consistency-manager.py

**Conversion Process**:
1. Take system prompt + user prompt from .md file
2. Convert JSON response schema to Python Data output
3. Create Langflow Component class with proper inputs/outputs
4. Same functionality, different format

**Medium Priority** (New development):
- 003-shot-generator.py (cinematography AI)
- 006-veo3-optimizer.py (enhanced prompts)

## Success Criteria
- 50+ quality shots from any screenplay
- Character consistency across all shots
- Professional cinematography choices
- VEO3-optimized prompts under 500 characters

## Ready to Start

This gives you a practical, AI-powered workflow that follows film industry standards without overengineering. Each component has one clear job, uses AI for smart decisions, and can be implemented quickly.

**Next Step**: Start with enhancing 000-screenplay-parser.py to better extract character descriptions and scene context, then build the character extractor component.

---

*Simple, effective, professional - ready for solo dev implementation.*