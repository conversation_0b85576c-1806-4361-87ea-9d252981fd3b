# Quick Debug Steps - Empty Subjects Issue

## 🚨 **URGENT: Check These 3 Things**

### **1. Check Debug Info Fields**
In your Subject Extractor output, look for:
```json
"debug_info": {
  "narrative_text_length": ???,
  "narrative_text_preview": "???",
  "raw_response_preview": "???"
}
```

**Tell me what you see for each of these values!**

### **2. Test Language Model Separately**
1. Disconnect Subject Extractor temporarily
2. Connect Text Input directly to Language Model
3. Put this in Text Input: "Extract subjects from this story: A girl wore a red cloak."
4. Run just the Language Model
5. **What output do you get?**

### **3. Check Connections Visually**
Make sure you have these exact connections:
```
[Text Input] ──┬─→ [Language Model] Input
               ├─→ [Subject Extractor] Narrative Text
               
[Language Model] Output ──→ [Subject Extractor] Language Model
```

## 🔧 **Quick Fixes to Try**

### **Fix 1: Simplify System Message**
In Language Model, try this shorter system message:
```
You are a helpful assistant that extracts subjects from stories and responds in JSON format.
```

### **Fix 2: Test with Minimal Text**
Put this in Text Input:
```
A girl wore a red cloak.
```

### **Fix 3: Try Different Model**
Switch to:
- OpenAI: `gpt-4o-mini` (cheaper, often works better)
- Anthropic: `claude-3-haiku-20240307` (faster)

## 📋 **What to Share**
Copy and paste:
1. The `debug_info` section from your output
2. What happens when you test Language Model alone
3. Which model/provider you're using
4. Your exact Text Input content

**This will tell us exactly what's wrong!**