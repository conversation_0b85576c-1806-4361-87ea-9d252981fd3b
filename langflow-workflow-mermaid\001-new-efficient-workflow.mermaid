flowchart LR
    %% Input
    FILE[File<br/>Narrative Text] --> SP_input[narrative_text]
    
    %% Component 1: Screenplay Parser (Pure Python)
    SP_input --> SP[Python Component<br/>001 Screenplay Parser<br/>🐍 Pure Python]
    SP --> SP_out[parsed_data]
    
    %% Component 2: Subject Extractor (Python + LLM)
    SP_out --> SE_parsed[parsed_screenplay]
    SE_parsed --> SE[Python Component<br/>002 Subject Extractor<br/>🐍 Python + AI]
    LM1[Language Model<br/>Gemini 2.5] --> SE_llm[llm]
    SE_llm --> SE
    SE --> SE_out[extracted_subjects]
    
    %% Component 3: Description Enhancer (Python + LLM)
    SE_out --> DE_subjects[extracted_subjects]
    DE_subjects --> DE[Python Component<br/>003 Description Enhancer<br/>🐍 Python + AI]
    LM1 --> DE_llm[llm]
    DE_llm --> DE
    DE --> DE_out[enhanced_subjects]
    
    %% Component 4: Shot Generator (Python + LLM)
    DE_out --> SG_enhanced[enhanced_subjects]
    SP_out --> SG_parsed[parsed_screenplay]
    SG_enhanced --> SG[Python Component<br/>004 Shot Generator<br/>🐍 Python + AI]
    SG_parsed --> SG
    LM1 --> SG_llm[llm]
    SG_llm --> SG
    TI_genre[Text Input<br/>Genre] --> SG_genre[genre]
    TI_duration[Text Input<br/>Duration] --> SG_duration[duration]
    SG_genre --> SG
    SG_duration --> SG
    SG --> SG_out[generated_shots]
    
    %% Component 5: Scene Descriptor (Pure Python)
    SG_out --> SD_shots[generated_shots]
    DE_out --> SD_subjects[enhanced_subjects]
    SD_shots --> SD[Python Component<br/>005 Scene Descriptor<br/>🐍 Pure Python]
    SD_subjects --> SD
    SD --> SD_out[scene_descriptions]
    
    %% Component 6: Consistency Manager (Pure Python)
    SD_out --> CM_scenes[scene_descriptions]
    DE_out --> CM_subjects[enhanced_subjects]
    CM_scenes --> CM[Python Component<br/>006 Consistency Manager<br/>🐍 Pure Python]
    CM_subjects --> CM
    CM --> CM_out[consistent_prompts]
    
    %% Component 7: VEO3 Optimizer (Python + LLM)
    CM_out --> VO_prompts[consistent_prompts]
    VO_prompts --> VO[Python Component<br/>007 VEO3 Optimizer<br/>🐍 Python + AI]
    LM1 --> VO_llm[llm]
    VO_llm --> VO
    TI_chars[Text Input<br/>Max Characters] --> VO_chars[max_characters]
    TI_ratio[Text Input<br/>Aspect Ratio] --> VO_ratio[aspect_ratio]
    VO_chars --> VO
    VO_ratio --> VO
    VO --> FINAL[veo3_prompts<br/>📹 Final Output]
    
    %% Styling
    classDef file fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef textinput fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef llm fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef python fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef pythonai fill:#fff8e1,stroke:#ffc107,stroke-width:3px
    classDef io fill:#ffecb3,stroke:#795548,stroke-width:1px
    classDef final fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    
    class FILE file
    class TI_genre,TI_duration,TI_chars,TI_ratio textinput
    class LM1 llm
    class SP,SD,CM python
    class SE,DE,SG,VO pythonai
    class SP_input,SP_out,SE_parsed,SE_out,DE_subjects,DE_out,SG_enhanced,SG_parsed,SG_out,SD_shots,SD_subjects,SD_out,CM_scenes,CM_subjects,CM_out,VO_prompts,SE_llm,DE_llm,SG_llm,VO_llm,SG_genre,SG_duration,VO_chars,VO_ratio io
    class FINAL final
