flowchart LR
    %% Input
    FILE[File<br/>Narrative Text] --> SP_input[narrative_text]

    %% Component 1: Screenplay Parser (Pure Python)
    SP_input --> SP[Python Component<br/>001 Screenplay Parser<br/>🐍 Pure Python]
    SP --> SP_out[parsed_data]

    %% Component 2: Character Extractor (Mostly Python)
    SP_out --> CE_parsed[parsed_screenplay]
    CE_parsed --> CE[Python Component<br/>002 Character Extractor<br/>🐍 Mostly Python]
    CE --> CE_out[extracted_characters]

    %% Component 3: Object Extractor (Mostly Python)
    SP_out --> OE_parsed[parsed_screenplay]
    OE_parsed --> OE[Python Component<br/>003 Object Extractor<br/>🐍 Mostly Python]
    OE --> OE_out[extracted_objects]

    %% Component 4: Environment Extractor (Mostly Python)
    SP_out --> EE_parsed[parsed_screenplay]
    EE_parsed --> EE[Python Component<br/>004 Environment Extractor<br/>🐍 Mostly Python]
    EE --> EE_out[extracted_environments]

    %% Component 5: Other Elements Extractor (Mostly Python)
    SP_out --> OTE_parsed[parsed_screenplay]
    OTE_parsed --> OTE[Python Component<br/>005 Other Elements Extractor<br/>🐍 Mostly Python]
    OTE --> OTE_out[extracted_other_elements]
    
    %% Component 6: Description Enhancer (Python + LLM for Creative Enhancement)
    CE_out --> DE_characters[extracted_characters]
    OE_out --> DE_objects[extracted_objects]
    EE_out --> DE_environments[extracted_environments]
    OTE_out --> DE_other[extracted_other_elements]
    DE_characters --> DE[Python Component<br/>006 Description Enhancer<br/>🐍 Python + AI for Creative Enhancement]
    DE_objects --> DE
    DE_environments --> DE
    DE_other --> DE
    LM1[Language Model<br/>Gemini 2.5] --> DE_llm[llm]
    DE_llm --> DE
    DE --> DE_out[enhanced_descriptions]

    %% Component 7: Shot Generator (Python + LLM for Director of Photography)
    DE_out --> SG_enhanced[enhanced_descriptions]
    SP_out --> SG_parsed[parsed_screenplay]
    SG_enhanced --> SG[Python Component<br/>007 Shot Generator<br/>🐍 Python + AI for Director of Photography]
    SG_parsed --> SG
    LM1 --> SG_llm[llm]
    SG_llm --> SG
    TI_genre[Text Input<br/>Genre] --> SG_genre[genre]
    TI_duration[Text Input<br/>Duration] --> SG_duration[duration]
    SG_genre --> SG
    SG_duration --> SG
    SG --> SG_out[generated_shots]
    
    %% Component 8: Scene Descriptor (Pure Python)
    SG_out --> SD_shots[generated_shots]
    DE_out --> SD_descriptions[enhanced_descriptions]
    SD_shots --> SD[Python Component<br/>008 Scene Descriptor<br/>🐍 Pure Python]
    SD_descriptions --> SD
    SD --> SD_out[storyboard_descriptions]

    %% Component 9: Consistency Manager (Pure Python)
    SD_out --> CM_scenes[storyboard_descriptions]
    DE_out --> CM_descriptions[enhanced_descriptions]
    CM_scenes --> CM[Python Component<br/>009 Consistency Manager<br/>🐍 Pure Python]
    CM_descriptions --> CM
    CM --> CM_out[consistent_storyboard_prompts]

    %% Component 10: Storyboard Optimizer (Python + LLM)
    CM_out --> SO_prompts[consistent_storyboard_prompts]
    SO_prompts --> SO[Python Component<br/>010 Storyboard Optimizer<br/>🐍 Python + AI for Final Optimization]
    LM1 --> SO_llm[llm]
    SO_llm --> SO
    TI_chars[Text Input<br/>Max Characters] --> SO_chars[max_characters]
    TI_ratio[Text Input<br/>Aspect Ratio] --> SO_ratio[aspect_ratio]
    SO_chars --> SO
    SO_ratio --> SO
    SO --> FINAL[storyboard_prompts<br/>🎬 Final Storyboard Output]
    
    %% Styling
    classDef file fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef textinput fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef llm fill:#e1f5fe,stroke:#2196f3,stroke-width:2px
    classDef python fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef pythonai fill:#fff8e1,stroke:#ffc107,stroke-width:3px
    classDef io fill:#ffecb3,stroke:#795548,stroke-width:1px
    classDef final fill:#e8f5e8,stroke:#4caf50,stroke-width:3px
    
    class FILE file
    class TI_genre,TI_duration,TI_chars,TI_ratio textinput
    class LM1 llm
    class SP,CE,OE,EE,OTE,SD,CM python
    class DE,SG,SO pythonai
    class SP_input,SP_out,CE_parsed,CE_out,OE_parsed,OE_out,EE_parsed,EE_out,OTE_parsed,OTE_out,DE_characters,DE_objects,DE_environments,DE_other,DE_out,SG_enhanced,SG_parsed,SG_out,SD_shots,SD_descriptions,SD_out,CM_scenes,CM_descriptions,CM_out,SO_prompts,DE_llm,SG_llm,SO_llm,SG_genre,SG_duration,SO_chars,SO_ratio io
    class FINAL final
