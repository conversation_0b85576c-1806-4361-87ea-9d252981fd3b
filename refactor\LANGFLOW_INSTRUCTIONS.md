# Langflow Connection Instructions

## Overview
This guide provides step-by-step instructions for connecting components in your Langflow workflow to replace the problematic 5-LLM chain with efficient Python components.

## Current vs New Workflow

### ❌ Current Problematic Setup
```
File → PT1 → LM1 → PT2 → LM2 → PT3 → LM3 → PT4 → LM4 → PT5 → LM5
```
**Issues:**
- 5 sequential LLM calls
- Token accumulation and limits
- Incomplete JSON responses ("possession"...)
- Error propagation through chain

### ✅ New Efficient Setup
```
File → Python(Parser) → Python(Extractor)+LM → Python(Enhancer)+LM → Python(Shots)+LM → Python(Scenes) → Python(Consistency) → Python(VEO3)+LM
```
**Benefits:**
- Only 3-4 strategic LLM calls
- Robust JSON validation and repair
- Isolated error handling
- 40-50% token reduction

## Step-by-Step Connection Guide

### Step 1: Remove Old Components
1. Delete all existing Prompt Template components (PT1-PT5)
2. Delete all Language Model components (LM1-LM5) except one
3. Keep your File input component

### Step 2: Add Python Components
For each component, create a new **Python Code** component in Langflow:

#### Component 1: Screenplay Parser
**Component Type:** Python Code
**Name:** "001 Screenplay Parser"
**Code:** Copy from `components/001-screenplay-parser.py`

**Connections:**
- **Input:** File component → `narrative_text`
- **Output:** `parsed_data` → Next component

#### Component 2: Subject Extractor
**Component Type:** Python Code
**Name:** "002 Subject Extractor"
**Code:** Copy from `components/002-subject-extractor.py`

**Connections:**
- **Input 1:** Screenplay Parser → `parsed_screenplay`
- **Input 2:** Language Model (Gemini) → `llm`
- **Output:** `extracted_subjects` → Next component

#### Component 3: Description Enhancer
**Component Type:** Python Code
**Name:** "003 Description Enhancer"
**Code:** Copy from `components/003-description-enhancer.py`

**Connections:**
- **Input 1:** Subject Extractor → `extracted_subjects`
- **Input 2:** Language Model (Gemini) → `llm`
- **Output:** `enhanced_subjects` → Next component

#### Component 4: Shot Generator
**Component Type:** Python Code
**Name:** "004 Shot Generator"
**Code:** Copy from `components/004-shot-generator.py`

**Connections:**
- **Input 1:** Description Enhancer → `enhanced_subjects`
- **Input 2:** Screenplay Parser → `parsed_screenplay`
- **Input 3:** Language Model (Gemini) → `llm`
- **Output:** `generated_shots` → Next component

#### Component 5: Scene Descriptor
**Component Type:** Python Code
**Name:** "005 Scene Descriptor"
**Code:** Copy from `components/005-scene-descriptor.py`

**Connections:**
- **Input 1:** Shot Generator → `generated_shots`
- **Input 2:** Description Enhancer → `enhanced_subjects`
- **Output:** `scene_descriptions` → Next component

#### Component 6: Consistency Manager
**Component Type:** Python Code
**Name:** "006 Consistency Manager"
**Code:** Copy from `components/006-consistency-manager.py`

**Connections:**
- **Input 1:** Scene Descriptor → `scene_descriptions`
- **Input 2:** Description Enhancer → `enhanced_subjects`
- **Output:** `consistent_prompts` → Next component

#### Component 7: VEO3 Optimizer
**Component Type:** Python Code
**Name:** "007 VEO3 Optimizer"
**Code:** Copy from `components/007-veo3-optimizer.py`

**Connections:**
- **Input 1:** Consistency Manager → `consistent_prompts`
- **Input 2:** Language Model (Gemini) → `llm`
- **Output:** `veo3_prompts` (Final output)

### Step 3: Configure Language Model
**Component Type:** Language Model (Gemini)
**Settings:**
- **Model:** gemini-2.5-flash (or your preferred Gemini model)
- **Temperature:** 0.7
- **Max Tokens:** 2048 (conservative to avoid cutoffs)
- **System Message:** Will be set by each Python component

**Connections:**
This single Language Model component connects to:
- Subject Extractor
- Description Enhancer  
- Shot Generator
- VEO3 Optimizer

### Step 4: Add Text Input Components (Optional)
For configuration parameters, add Text Input components:

#### Genre Input
**Component Type:** Text Input
**Name:** "Genre"
**Default Value:** "drama"
**Connection:** → Shot Generator

#### Duration Input
**Component Type:** Text Input
**Name:** "Shot Duration"
**Default Value:** "8 seconds"
**Connection:** → Shot Generator

#### Character Limit Input
**Component Type:** Text Input
**Name:** "Max Characters"
**Default Value:** "500"
**Connection:** → VEO3 Optimizer

## Component Configuration Details

### Python Component Setup
For each Python component:

1. **Create new Python Code component**
2. **Copy the component code** from the respective `.py` file
3. **Set component name** (e.g., "001 Screenplay Parser")
4. **Configure inputs** according to the specifications
5. **Test component individually** before connecting

### Language Model Configuration
**Important Settings for Gemini 2.5:**
```json
{
  "model": "gemini-2.5-flash",
  "temperature": 0.7,
  "max_tokens": 2048,
  "top_p": 0.9,
  "frequency_penalty": 0,
  "presence_penalty": 0
}
```

**Why these settings:**
- **max_tokens: 2048** - Conservative limit to prevent cutoffs
- **temperature: 0.7** - Balance between creativity and consistency
- **top_p: 0.9** - Good quality while avoiding repetition

## Testing Strategy

### Phase 1: Individual Component Testing
Test each component separately:

1. **Screenplay Parser**
   - Input: Your narrative text
   - Expected: Structured JSON with scenes and characters
   - Validation: Check all required fields present

2. **Subject Extractor**
   - Input: Parsed screenplay + LLM
   - Expected: Complete JSON with subjects
   - Validation: No truncated responses like "possession"...

3. **Description Enhancer**
   - Input: Extracted subjects + LLM
   - Expected: Enhanced descriptions
   - Validation: All subjects have complete enhanced_phrase

### Phase 2: Chain Testing
Connect components incrementally:

1. **File → Parser** (test first)
2. **File → Parser → Extractor** (test second)
3. **File → Parser → Extractor → Enhancer** (test third)
4. Continue adding one component at a time

### Phase 3: Full Workflow Testing
Test complete workflow with your data:
- Monitor token usage at each LLM call
- Check for incomplete JSON responses
- Verify final VEO3 prompts are complete and under character limits

## Troubleshooting

### Issue: Incomplete JSON Responses
**Symptoms:** Responses end with "..." or missing closing brackets
**Solution:** 
- Check `json_validator.py` is working
- Reduce max_tokens if needed
- Verify retry logic is functioning

### Issue: Component Connection Errors
**Symptoms:** Data not flowing between components
**Solution:**
- Check input/output names match exactly
- Verify data types are compatible
- Test components individually first

### Issue: LLM API Errors
**Symptoms:** API timeouts or rate limits
**Solution:**
- Implement exponential backoff in `llm_helper.py`
- Check API key and quota
- Reduce concurrent requests

### Issue: Token Limit Exceeded
**Symptoms:** Requests rejected for being too long
**Solution:**
- Implement prompt chunking
- Use simplified prompts for retry
- Check token counting logic

## Performance Monitoring

### Metrics to Track
1. **Token Usage per Component**
   - Target: <500 tokens per LLM call
   - Monitor: Total tokens across workflow

2. **Response Completeness**
   - Target: >95% complete JSON responses
   - Monitor: JSON validation success rate

3. **Processing Time**
   - Target: <30 seconds total workflow
   - Monitor: Time per component

4. **Error Rate**
   - Target: <5% component failures
   - Monitor: Retry frequency and success

### Success Criteria
- ✅ No more "possession"... truncated responses
- ✅ Complete JSON at every step
- ✅ 40-50% reduction in total tokens
- ✅ Faster overall processing
- ✅ Better error handling and recovery

## Next Steps
1. Implement components in order (001 → 007)
2. Test each component individually
3. Connect incrementally
4. Monitor performance metrics
5. Optimize based on results
