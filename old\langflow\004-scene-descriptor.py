from langflow.custom import Component
from langflow.io import DataInput, MessageTextInput, Output
from langflow.schema import Data

class SceneDescriptor(Component):
    display_name = "Scene Descriptor"
    description = "Creates detailed scene descriptions for text-to-image generation"
    
    inputs = [
        DataInput(name="generated_shots", display_name="Generated Shots"),
        DataInput(name="enhanced_subjects", display_name="Enhanced Subjects"),
        MessageTextInput(name="current_scene", display_name="Current Scene Context", value="")
    ]
    
    outputs = [
        Output(display_name="Scene Descriptions", name="scene_descriptions", method="create_scene_descriptions")
    ]
    
    def create_scene_descriptions(self) -> Data:
        shots_data = self.generated_shots.data
        subjects_data = self.enhanced_subjects.data
        scene_context = self.current_scene
        
        system_prompt = """You are an expert AI assistant specializing in creating detailed scene descriptions for text-to-image generation. Your primary purpose is to take a narrative context and subject descriptions and create a vivid, detailed scene that can be visualized by an image generation system.

Your expertise includes:
1. Creating detailed, visually rich scene descriptions
2. Incorporating multiple subjects into a coherent scene
3. Maintaining narrative consistency with the original story
4. Adding appropriate environmental details, lighting, mood, and atmosphere
5. Positioning subjects in a way that tells a story or captures a key moment

When creating scene descriptions:
- Focus on visual elements that can be rendered in an image
- Include details about positioning, lighting, perspective, and mood
- Maintain consistency with the narrative context
- Incorporate all relevant subjects in appropriate ways
- Create scenes that capture key moments or emotions from the narrative

Your scene descriptions should be self-contained and detailed enough that an image generation system can create a complete, coherent image without additional context."""

        # Process each shot to create detailed scene descriptions
        scene_descriptions_data = self._create_detailed_scenes(shots_data, subjects_data, scene_context)
        
        return Data(data=scene_descriptions_data)
    
    def _create_detailed_scenes(self, shots_data, subjects_data, scene_context):
        """
        This method would typically call an LLM API to create scene descriptions.
        For implementation, you would integrate with your preferred LLM service.
        """
        scene_descriptions = []
        shots = shots_data.get("shots", [])
        enhanced_subjects = subjects_data.get("enhanced_subjects", [])
        
        for shot in shots:
            # Create detailed scene description for each shot
            scene_desc = {
                "shot_number": shot["shot_number"],
                "scene_description": self._generate_scene_description(shot, enhanced_subjects, scene_context),
                "included_subjects": self._identify_included_subjects(shot, enhanced_subjects),
                "scene_elements": self._extract_scene_elements(shot)
            }
            scene_descriptions.append(scene_desc)
        
        return {
            "scene_descriptions": scene_descriptions,
            "total_scenes": len(scene_descriptions)
        }
    
    def _generate_scene_description(self, shot, enhanced_subjects, scene_context):
        """Generate a detailed, visually rich scene description"""
        # Placeholder - would use AI to create detailed description
        base_description = shot.get("description", "")
        camera_work = shot.get("camera_work", {})
        
        # Combine shot description with camera work and visual elements
        scene_description = f"{camera_work.get('angle', 'Medium shot')}: {base_description}"
        
        return scene_description
    
    def _identify_included_subjects(self, shot, enhanced_subjects):
        """Identify which subjects are included in this scene"""
        included = []
        characters_present = shot.get("characters_present", [])
        visual_elements = shot.get("visual_elements", [])
        
        for char in characters_present:
            included.append({
                "identifier": char.lower().replace(" ", "_"),
                "role_in_scene": "character in scene"
            })
        
        for element in visual_elements:
            included.append({
                "identifier": element["identifier"],
                "role_in_scene": "visual element"
            })
        
        return included
    
    def _extract_scene_elements(self, shot):
        """Extract key visual elements of the scene"""
        scene_context = shot.get("scene_context", {})
        
        return {
            "time_of_day": scene_context.get("time_of_day", "unknown"),
            "lighting": "natural lighting",  # Would be determined by AI
            "weather": "clear",  # Would be determined by AI
            "mood": scene_context.get("mood", "neutral"),
            "perspective": shot.get("camera_work", {}).get("angle", "medium shot")
        }